
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, ChartLine, Syringe, Brain, PillBottle, Calendar, User, Calendar as CalendarIcon } from "lucide-react";
import { PatientAnalysis } from "@/components/growth-curves/PatientAnalysis";
import { VaccineAnalysis } from "./VaccineAnalysis";
import { DNPMAnalysis } from "./dnpm/DNPMAnalysis";
import { SupplementationAnalysis } from "./SupplementationAnalysis";
import { useState } from "react";
import { PatientSummaryHeader } from "./PatientSummaryHeader";

interface PatientDashboardProps {
  data: {
    age: number;
    weight?: number;
    height?: number;
    gender: "male" | "female";
    headCircumference?: number;
    exclusiveBreastfeeding: boolean;
    hasRiskFactors: boolean;
    birthWeight?: number;
    maturity: "Term" | "Pre-term";
    name?: string;
    birthDate?: string;
  };
  onBack: () => void;
}

export function PatientDashboard({ data, onBack }: PatientDashboardProps) {
  const [activeTab, setActiveTab] = useState("growth");

  const handleTabChange = (value: string) => {
    console.log(`🔄 Alterando para a aba: ${value}`);
    setActiveTab(value);
  };

  return (
    <div className="space-y-8 animate-fade-in-up">
      <PatientSummaryHeader 
        patientData={data} 
        onBack={onBack}
      />

      <Card className="overflow-hidden border-primary/10 bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-800 dark:via-slate-800/70 dark:to-slate-800 shadow-lg">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          {/* Mobile Select */}
          <div className="md:hidden w-full p-1 bg-white dark:bg-slate-800 border-b border-primary/10">
            <Select value={activeTab} onValueChange={handleTabChange}>
              <SelectTrigger className="w-full bg-white dark:bg-slate-800 border-primary/20 hover:border-primary/40 transition-colors">
                <SelectValue placeholder={
                  <div className="flex items-center gap-2 text-primary/70">
                    <ChartLine className="h-4 w-4" />
                    <span>Selecione uma análise</span>
                  </div>
                } />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="growth" className="cursor-pointer hover:bg-primary/5 transition-colors">
                  <div className="flex items-center gap-2 py-1">
                    <ChartLine className="h-4 w-4 text-orange-500" />
                    <span className="font-medium text-orange-700 dark:text-orange-400">Crescimento</span>
                  </div>
                </SelectItem>
                <SelectItem value="vaccines" className="cursor-pointer hover:bg-primary/5 transition-colors">
                  <div className="flex items-center gap-2 py-1">
                    <Syringe className="h-4 w-4 text-purple-500" />
                    <span className="font-medium text-purple-700 dark:text-purple-400">Vacinas</span>
                  </div>
                </SelectItem>
                <SelectItem value="development" className="cursor-pointer hover:bg-primary/5 transition-colors">
                  <div className="flex items-center gap-2 py-1">
                    <Brain className="h-4 w-4 text-blue-500" />
                    <span className="font-medium text-blue-700 dark:text-blue-400">DNPM</span>
                  </div>
                </SelectItem>
                <SelectItem value="supplementation" className="cursor-pointer hover:bg-primary/5 transition-colors">
                  <div className="flex items-center gap-2 py-1">
                    <PillBottle className="h-4 w-4 text-green-500" />
                    <span className="font-medium text-green-700 dark:text-green-400">Suplementação</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Desktop Tabs */}
          <TabsList className="hidden md:flex w-full p-1 bg-white dark:bg-slate-800 border-b border-primary/10">
            <div className="container flex flex-wrap justify-center gap-2 py-2">
              <TabsTrigger 
                value="growth"
                className="data-[state=active]:bg-orange-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-orange-100 dark:hover:bg-orange-900/20 group text-sm"
              >
                <div className="flex items-center gap-2">
                  <ChartLine className="h-5 w-5 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium">Crescimento</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="vaccines"
                className="data-[state=active]:bg-purple-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-purple-100 dark:hover:bg-purple-900/20 group text-sm"
              >
                <div className="flex items-center gap-2">
                  <Syringe className="h-5 w-5 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium">Vacinas</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="development"
                className="data-[state=active]:bg-blue-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-blue-100 dark:hover:bg-blue-900/20 group text-sm"
              >
                <div className="flex items-center gap-2">
                  <Brain className="h-5 w-5 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium">DNPM</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="supplementation"
                className="data-[state=active]:bg-green-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-green-100 dark:hover:bg-green-900/20 group text-sm"
              >
                <div className="flex items-center gap-2">
                  <PillBottle className="h-5 w-5 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium">Suplementação</span>
                </div>
              </TabsTrigger>
            </div>
          </TabsList>

          <div className="mt-6 container">
            <TabsContent value="growth" className="animate-fade-in-up">
              <PatientAnalysis 
                initialData={{
                  age: data.age,
                  gender: data.gender,
                  weight: data.weight,
                  height: data.height,
                  headCircumference: data.headCircumference,
                  maturity: data.maturity
                }}
                readOnly
              />
            </TabsContent>

            <TabsContent value="vaccines" className="animate-fade-in-up">
              <VaccineAnalysis ageInMonths={data.age} />
            </TabsContent>

            <TabsContent value="development" className="animate-fade-in-up">
              <Card className="p-6 bg-white dark:bg-slate-800 backdrop-blur-sm border-primary/10 shadow-lg">
                <DNPMAnalysis ageInMonths={data.age} />
              </Card>
            </TabsContent>

            <TabsContent value="supplementation" className="animate-fade-in-up">
              <Card className="p-6 bg-white dark:bg-slate-800 backdrop-blur-sm border-primary/10 shadow-lg">
                <SupplementationAnalysis data={data} />
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </Card>
    </div>
  );
}
