
import React from 'react';
import { CommandGroup, CommandItem } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { SearchResult } from "../types";
import { cn } from "@/lib/utils";

interface SearchResultGroupProps {
  title: string;
  results: SearchResult[];
  onSelect: (result: SearchResult) => void;
  formatBrands?: (brands: string | null | undefined) => string;
  icon: React.ReactNode;
}

export const SearchResultGroup: React.FC<SearchResultGroupProps> = ({
  title,
  results,
  onSelect,
  formatBrands,
  icon
}) => {
  if (results.length === 0) return null;

  return (
    <CommandGroup
      heading={title}
      className="px-0"
      headingClassName="sticky top-0 z-10 bg-gray-100/80 dark:bg-slate-700/80 backdrop-blur-md px-4 py-2 text-xs font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200/50 dark:border-gray-700/50"
    >
      <div className="flex flex-col">
        {results.map((result) => (
          <CommandItem
            key={result.id}
            onSelect={() => onSelect(result)}
            className="flex items-start px-4 py-3 hover:bg-gray-50/80 dark:hover:bg-slate-700/50 active:bg-gray-100 dark:active:bg-slate-600/50 cursor-pointer transition-colors rounded-md m-1"
            value={`${result.name}${result.brands || ''}${result.category?.name || ''}${result.code_range || ''}`}
          >
            {/* Ícone com fundo */}
            <div className="flex-shrink-0 mr-3 mt-0.5 bg-gray-100/80 dark:bg-slate-700/80 p-1.5 rounded-md">
              {icon}
            </div>

            {/* Conteúdo principal */}
            <div className="flex flex-col flex-1 min-w-0">
              {/* Nome do item */}
              <div className="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                {result.type === 'icd10' && result.code_range
                  ? (
                    <>
                      <span className="inline-flex items-center justify-center bg-cyan-100 dark:bg-cyan-900/40 text-cyan-800 dark:text-cyan-300 text-xs font-medium px-1.5 py-0.5 rounded-md">
                        {result.code_range}
                      </span>
                      {result.name}
                    </>
                  )
                  : result.name
                }
              </div>

              {/* Linha inferior com informações adicionais e badge */}
              <div className="flex flex-wrap items-center gap-x-2 mt-1">
                {/* Marcas/descrição */}
                {formatBrands && result.brands && (
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate max-w-[70%]">
                    {formatBrands(result.brands)}
                  </div>
                )}
                {!result.brands && result.description && (
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate max-w-[70%]">
                    {result.description}
                  </div>
                )}

                {/* Badge de categoria */}
                {result.category && (
                  <Badge
                    variant="outline"
                    className="bg-gray-100/80 dark:bg-slate-700/80 text-gray-700 dark:text-gray-300 border-none text-[10px] px-2 py-0.5 h-auto font-medium whitespace-nowrap rounded-md"
                  >
                    {result.category.name}
                  </Badge>
                )}
              </div>
            </div>
          </CommandItem>
        ))}
      </div>
    </CommandGroup>
  );
};
