import { useEffect, useState, useRef } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { useToast } from "../ui/use-toast";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useTabFocusProtection } from "@/hooks/useTabFocusProtection";

export default function AdminRoute() {
  const supabase = useSupabaseClient();
  const location = useLocation();
  const { user, profile, isAdmin: authIsAdmin, isLoading: authIsLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState<boolean | null>(null);
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Debug logging
  console.log("🔍 AdminRoute - Estado atual:", {
    pathname: location.pathname,
    user: user?.id,
    authIsAdmin,
    authIsLoading,
    isAdmin,
    isLoading,
    hasInitialized,
    userPermissions
  });

  // Usar nosso hook simplificado de proteção contra perda de estado ao alternar tabs
  // Não precisamos inicializar o hook aqui, pois ele já está sendo inicializado globalmente
  const tabProtection = useTabFocusProtection();

  const currentPath = location.pathname;
  const currentResource = currentPath.split('/admin/')[1]?.split('/')[0] || 'dashboard';

  // Referência para armazenar o estado de admin
  const cachedAdminState = useRef<{
    isAdmin: boolean | null;
    isSuperAdmin: boolean | null;
    userPermissions: string[];
  }>({
    isAdmin: null,
    isSuperAdmin: null,
    userPermissions: []
  });

  // Referência para controlar se devemos verificar o status de admin após mudança de visibilidade
  const skipAdminCheckAfterVisibilityChange = useRef(false);

  useEffect(() => {
    console.log("🚀 AdminRoute - useEffect executado:", {
      authIsLoading,
      user: user?.id,
      authIsAdmin
    });

    const checkAdminStatus = async () => {
      console.log("🔄 AdminRoute - checkAdminStatus iniciado");

      // Se a proteção contra perda de estado estiver ativa, usar o estado em cache
      if (tabProtection.isProtectionActive()) {
        console.log("🛡️ AdminRoute - Usando proteção de tab, cache:", cachedAdminState.current);

        // Se temos um estado em cache, usá-lo
        if (cachedAdminState.current.isAdmin !== null) {
          setIsAdmin(cachedAdminState.current.isAdmin);
          setIsSuperAdmin(cachedAdminState.current.isSuperAdmin);
          setUserPermissions(cachedAdminState.current.userPermissions);
          console.log("✅ AdminRoute - Estado restaurado do cache");
        }

        setIsLoading(false);
        setHasInitialized(true);
        return;
      }

      try {
        setIsLoading(true);


        // Usar o usuário do hook useAuth
        if (!user) {
          console.log("❌ AdminRoute - Usuário não encontrado, redirecionando");
          toast({
            variant: "destructive",
            title: "Acesso negado",
            description: "Você precisa estar logado para acessar esta página.",
          });
          navigate("/");
          return;
        }

        console.log("👤 AdminRoute - Usuário encontrado:", user.id);

        // Usar o perfil do hook useAuth
        if (!authIsAdmin) {
          console.log("❌ AdminRoute - Usuário não é admin, redirecionando");
          toast({
            variant: "destructive",
            title: "Acesso negado",
            description: "Você não tem permissão para acessar esta página.",
          });
          navigate("/");
          return;
        }

        console.log("✅ AdminRoute - Usuário é admin, continuando verificações");

        setIsAdmin(true);
        console.log("🔧 AdminRoute - setIsAdmin(true) executado");

        console.log("🔍 AdminRoute - Buscando roles do usuário...");
        const { data: roleData, error: roleError } = await supabase
          .from("admin_user_roles")
          .select("role_id")
          .eq("user_id", user.id);

        if (roleError) {
          console.log("❌ AdminRoute - Erro ao buscar roles:", roleError);
          throw roleError;
        }

        console.log("📋 AdminRoute - Roles encontrados:", roleData);

        const { data: superAdminData } = await supabase
          .from("admin_roles")
          .select("id, name")
          .eq("name", "super_admin")
          .maybeSingle();

        console.log("👑 AdminRoute - Super admin data:", superAdminData);

        const superAdminRoleId = superAdminData?.id;
        const isSuperAdmin = roleData.some(role => role.role_id === superAdminRoleId);
        setIsSuperAdmin(isSuperAdmin);

        console.log("🎯 AdminRoute - É super admin?", isSuperAdmin);

        // Cache the admin state
        cachedAdminState.current = {
          isAdmin: true,
          isSuperAdmin: isSuperAdmin,
          userPermissions: isSuperAdmin ? ['all'] : []
        };

        if (isSuperAdmin) {
          setUserPermissions(['all']);
          setIsLoading(false);
          return;
        }

        try {
          const { data: permissionsData, error: permissionsError } = await supabase
            .from("admin_user_permissions")
            .select("resource")
            .eq("user_id", user.id);

          if (permissionsError) throw permissionsError;

          const permissions = permissionsData.map(p => p.resource);
          const allPermissions = ['dashboard', ...permissions];
          setUserPermissions(allPermissions);

          // Update cached admin state with permissions
          cachedAdminState.current = {
            ...cachedAdminState.current,
            userPermissions: allPermissions
          };

          if (currentResource !== 'dashboard' && !permissions.includes(currentResource)) {
            toast({
              variant: "destructive",
              title: "Acesso restrito",
              description: "Você não tem permissão para acessar este módulo.",
            });
            navigate("/admin/dashboard");
          }
        } catch {
          setUserPermissions(['all']);

          // Update cached admin state with all permissions
          cachedAdminState.current = {
            ...cachedAdminState.current,
            userPermissions: ['all']
          };
        }

      } catch (error) {
        console.log("❌ AdminRoute - Erro geral:", error);
        toast({
          variant: "destructive",
          title: "Erro",
          description: "Não foi possível verificar suas permissões de administrador.",
        });
        navigate("/");
      } finally {
        console.log("🏁 AdminRoute - Finalizando verificações");
        setIsLoading(false);
        setHasInitialized(true);
      }
    };

    // Só executar se o usuário estiver autenticado e não estiver carregando
    if (!authIsLoading && user) {
      console.log("▶️ AdminRoute - Executando checkAdminStatus");
      checkAdminStatus();
    } else if (!authIsLoading && !user) {
      console.log("🚫 AdminRoute - Sem usuário, redirecionando para home");
      // Se não estiver carregando e não tiver usuário, redirecionar
      navigate("/");
    } else {
      console.log("⏳ AdminRoute - Aguardando autenticação...");
    }
  }, [user, authIsAdmin, authIsLoading, supabase, toast, navigate, currentResource]);

  useEffect(() => {
    if (isAdmin && !isLoading && !isSuperAdmin && userPermissions.length > 0 && userPermissions[0] !== 'all') {
      const currentResource = currentPath.split('/admin/')[1]?.split('/')[0] || 'dashboard';

      if (currentResource !== 'dashboard' && !userPermissions.includes(currentResource)) {
        toast({
          variant: "destructive",
          title: "Acesso restrito",
          description: "Você não tem permissão para acessar este módulo.",
        });
        navigate("/admin/dashboard");
      }
    }
  }, [currentPath, userPermissions, isAdmin, isLoading, isSuperAdmin, navigate, toast]);

  // Mostrar indicador de carregamento se estiver carregando a autenticação ou as permissões
  if (authIsLoading || isLoading || !hasInitialized) {
    console.log("⏳ AdminRoute - Renderizando loading:", {
      authIsLoading,
      isLoading,
      hasInitialized
    });
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  console.log("🎨 AdminRoute - Renderização final:", {
    isAdmin,
    willRenderOutlet: !!isAdmin
  });

  // Só renderizar o conteúdo se o usuário for admin
  return isAdmin ? <Outlet /> : null;
}
