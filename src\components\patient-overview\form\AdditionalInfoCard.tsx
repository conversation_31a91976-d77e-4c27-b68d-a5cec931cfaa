
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Baby, AlertTriangle } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface AdditionalInfoCardProps {
  exclusiveBreastfeeding: boolean;
  hasRiskFactors: boolean;
  onBreastfeedingChange: (checked: boolean) => void;
  onRiskFactorsChange: (checked: boolean) => void;
}

export function AdditionalInfoCard({
  exclusiveBreastfeeding,
  hasRiskFactors,
  onBreastfeedingChange,
  onRiskFactorsChange,
}: AdditionalInfoCardProps) {
  const riskFactorsInfo = [
    "Prematuridade",
    "Baixo peso ao nascer",
    "Crescimento rápido (>p90)",
    "Alimentação complementar pobre em ferro",
    "Aleitamento materno exclusivo prolongado além de 6 meses sem suplementação"
  ].join("\n");

  return (
    <Card className="p-4 bg-white/80 dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="exclusiveBreastfeeding" className="flex items-center gap-2 cursor-pointer">
            <Baby className="h-4 w-4 text-purple-500 dark:text-purple-400" />
            <span className="text-gray-800 dark:text-gray-200">Aleitamento Materno Exclusivo</span>
          </Label>
          <Switch
            id="exclusiveBreastfeeding"
            checked={exclusiveBreastfeeding}
            onCheckedChange={onBreastfeedingChange}
            className="data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="hasRiskFactors" className="flex items-center gap-2 cursor-pointer">
            <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
            <span className="text-gray-800 dark:text-gray-200">Fatores de Risco</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
                </TooltipTrigger>
                <TooltipContent className="whitespace-pre-line bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 p-2 max-w-sm text-gray-800 dark:text-gray-200">
                  {riskFactorsInfo}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <Switch
            id="hasRiskFactors"
            checked={hasRiskFactors}
            onCheckedChange={onRiskFactorsChange}
            className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
          />
        </div>
      </div>
    </Card>
  );
}
