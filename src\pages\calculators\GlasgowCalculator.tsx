
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, Eye, Hand, MessageSquare } from "lucide-react";
import { Link } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { useSiteAnalytics } from "@/hooks/useMedicationAnalytics";

interface GlasgowValues {
  eyeOpening: number | null;
  motorResponse: number | null;
  verbalResponse: number | null;
}

const GlasgowCalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['glasgow'];
  const { trackCalculatorUse } = useSiteAnalytics();

  const [values, setValues] = useState<GlasgowValues>({
    eyeOpening: null,
    motorResponse: null,
    verbalResponse: null,
  });

  const criteria = {
    eyeOpening: [
      { value: 4, label: "Espontânea" },
      { value: 3, label: "Ao chamado" },
      { value: 2, label: "Estímulo álgico" },
      { value: 1, label: "Não responde" },
    ],
    motorResponse: [
      { value: 6, label: "Movimentos com propósito" },
      { value: 5, label: "Localiza dor" },
      { value: 4, label: "Retira membros à dor" },
      { value: 3, label: "Flexão anormal (decorticação)" },
      { value: 2, label: "Extensão anormal (descerebração)" },
      { value: 1, label: "Não responde" },
    ],
    verbalResponse: [
      { value: 5, label: "Lalação, sons próprios da idade" },
      { value: 4, label: "Choro consolável" },
      { value: 3, label: "Choro inconsolável" },
      { value: 2, label: "Grunidos ou gemência à dor" },
      { value: 1, label: "Não responde" },
    ],
  };

  const handleChange = (key: keyof GlasgowValues, value: string) => {
    setValues((prev) => ({
      ...prev,
      [key]: parseInt(value, 10),
    }));
  };

  const calculateScore = () => {
    const total = Object.values(values).reduce((sum, value) => sum + (value || 0), 0);
    return total;
  };

  const getInterpretation = (score: number) => {
    if (score >= 13) {
      return {
        text: "Trauma craniano leve",
        description: "Monitoramento contínuo recomendado",
        color: "text-green-600 dark:text-green-400",
      };
    } else if (score >= 9) {
      return {
        text: "Trauma craniano moderado",
        description: "Avaliação neurológica detalhada necessária",
        color: "text-yellow-600 dark:text-yellow-400",
      };
    }
    return {
      text: "Trauma craniano grave",
      description: "Necessidade de intubação e cuidados intensivos",
      color: "text-red-600 dark:text-red-400",
    };
  };

  const score = calculateScore();
  const interpretation = getInterpretation(score);
  const allFieldsFilled = Object.values(values).every((value) => value !== null);

  // Fazer tracking quando todos os campos estiverem preenchidos
  React.useEffect(() => {
    if (allFieldsFilled && score > 0) {
      trackCalculatorUse('Glasgow', {
        eye_opening: values.eyeOpening,
        motor_response: values.motorResponse,
        verbal_response: values.verbalResponse,
        total_score: score,
        classification: interpretation.classification
      });
    }
  }, [allFieldsFilled, score, values, interpretation.classification, trackCalculatorUse]);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO {...seoData} />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Escala de Glasgow Pediátrica
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação do nível de consciência em crianças baseada em três componentes principais
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-primary dark:text-blue-400" />
                  <Label className="text-base font-medium text-gray-800 dark:text-gray-200">Abertura Ocular</Label>
                </div>
                <Select
                  value={values.eyeOpening?.toString() || ""}
                  onValueChange={(value) => handleChange("eyeOpening", value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {criteria.eyeOpening.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label} ({option.value} pontos)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Hand className="h-5 w-5 text-primary dark:text-blue-400" />
                  <Label className="text-base font-medium text-gray-800 dark:text-gray-200">Melhor Resposta Motora</Label>
                </div>
                <Select
                  value={values.motorResponse?.toString() || ""}
                  onValueChange={(value) => handleChange("motorResponse", value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {criteria.motorResponse.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label} ({option.value} pontos)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-primary dark:text-blue-400" />
                  <Label className="text-base font-medium text-gray-800 dark:text-gray-200">Melhor Resposta Verbal</Label>
                </div>
                <Select
                  value={values.verbalResponse?.toString() || ""}
                  onValueChange={(value) => handleChange("verbalResponse", value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {criteria.verbalResponse.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label} ({option.value} pontos)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {allFieldsFilled && (
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary dark:text-blue-400">
                    {score} pontos
                  </div>
                  <div className={`text-xl font-semibold ${interpretation.color}`}>
                    {interpretation.text}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                    {interpretation.description}
                    <p className="mt-2 text-xs">
                      Nota: Esta avaliação deve ser considerada em conjunto com outros parâmetros clínicos para decisões terapêuticas.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default GlasgowCalculator;
