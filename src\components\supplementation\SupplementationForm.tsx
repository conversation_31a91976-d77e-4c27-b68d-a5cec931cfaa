import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Calculator, Baby, Scale, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { SupplementationInput } from "@/types/supplementation";

interface SupplementationFormProps {
  onCalculate: (input: SupplementationInput) => void;
}

export const SupplementationForm = ({ onCalculate }: SupplementationFormProps) => {
  const [formData, setFormData] = useState({
    ageInDays: "",
    currentWeight: "",
    birthWeight: "",
    maturity: "Term" as "Term" | "Pre-term",
    exclusiveBreastfeeding: false,
    hasRiskFactors: false
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const input: SupplementationInput = {
      ageInDays: parseInt(formData.ageInDays),
      currentWeight: parseInt(formData.currentWeight),
      birthWeight: parseInt(formData.birthWeight),
      maturity: formData.maturity,
      exclusiveBreastfeeding: formData.exclusiveBreastfeeding,
      hasRiskFactors: formData.hasRiskFactors
    };

    // Log do input processado
    
    onCalculate(input);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Baby className="h-4 w-4" />
            Idade (dias)
          </Label>
          <Input
            type="number"
            placeholder="Em dias"
            value={formData.ageInDays}
            onChange={(e) => setFormData({ ...formData, ageInDays: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Peso Atual (g)
          </Label>
          <Input
            type="number"
            placeholder="Em gramas"
            value={formData.currentWeight}
            onChange={(e) => setFormData({ ...formData, currentWeight: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Peso ao Nascer (g)
          </Label>
          <Input
            type="number"
            placeholder="Em gramas"
            value={formData.birthWeight}
            onChange={(e) => setFormData({ ...formData, birthWeight: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Baby className="h-4 w-4" />
            Maturidade
          </Label>
          <Select
            value={formData.maturity}
            onValueChange={(value: "Term" | "Pre-term") => 
              setFormData({ ...formData, maturity: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione a maturidade" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Term">A termo</SelectItem>
              <SelectItem value="Pre-term">Prematuro</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card className="p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="exclusiveBreastfeeding">Aleitamento Materno Exclusivo</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Aleitamento Materno Exclusivo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="exclusiveBreastfeeding"
                checked={formData.exclusiveBreastfeeding}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, exclusiveBreastfeeding: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="hasRiskFactors">Fatores de Risco</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Veja nosso FAQ para mais informações sobre fatores de risco.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="hasRiskFactors"
                checked={formData.hasRiskFactors}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, hasRiskFactors: checked })
                }
              />
            </div>
          </div>
        </Card>
      </div>

      <Button type="submit" className="w-full">
        <Calculator className="mr-2 h-4 w-4" />
        Calcular Suplementação
      </Button>
    </form>
  );
};