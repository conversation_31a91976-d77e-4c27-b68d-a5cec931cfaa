
import { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate, Link, useLocation } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { ChevronUp, Home } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useWeight } from "@/hooks/useWeight";
import { useAge } from "@/hooks/useAge";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { MobileView } from "@/components/medication/details/MobileView";
import { DesktopView } from "@/components/medication/details/DesktopView";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { useLoading } from "@/context/LoadingContext";
import { useMedicationAnalytics } from "@/hooks/useMedicationAnalytics";
import { useMedicationCategoriesWithMedications } from "@/hooks/useMedicationData";

export default function MedicationDetails() {
  const navigate = useNavigate();
  const { slug } = useParams();
  const location = useLocation();
  const { weight, setWeight, displayWeight, setTempWeight } = useWeight();
  const { age, setAge } = useAge();
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { stopLoading } = useLoading();
  const { trackMedicationView } = useMedicationAnalytics();

  // Memoize query key to prevent unnecessary re-executions
  const queryKey = useMemo(() => ["medication", slug], [slug]);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []); // Remover dependências desnecessárias

  // Efeito para verificar se há algum estado passado na navegação
  useEffect(() => {
    if (location.state?.selectedCategory) {
      // Se houver um estado com categoria selecionada, podemos usá-lo aqui
      // Limpar o estado para não persistir em navegações futuras
      window.history.replaceState({}, document.title);
    }
  }, [location]);

  const { data: medication, isLoading, isFetching, error } = useQuery({
    queryKey,
    queryFn: async () => {
      console.log('🔍 [MEDICATION QUERY] Iniciando query para slug:', slug);

      if (!slug) {
        console.log('⚠️ [MEDICATION QUERY] Slug não fornecido');
        stopLoading();
        return null;
      }

      console.log('🚀 [MEDICATION QUERY] Executando query no Supabase...');
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          id,
          name,
          slug,
          description,
          brands,
          contraindications,
          guidelines,
          scientific_references,
          category_id,
          pedbook_medication_categories!inner (
            name
          ),
          pedbook_medication_use_cases (
            id,
            name,
            description,
            display_order,
            pedbook_medication_dosages (
              id,
              name,
              dosage_template,
              summary,
              age_group,
              medication_id
            )
          )
        `)
        .eq("slug", slug)
        .maybeSingle();

      console.log('📊 [MEDICATION QUERY] Resultado da query:', { data, error });

      if (error) {
        console.error('❌ [MEDICATION QUERY] Erro na query:', error);
        stopLoading();
        if (error.code === "PGRST116") {
          console.log('⚠️ [MEDICATION QUERY] Medicamento não encontrado (PGRST116)');
          return null;
        }
        throw error;
      }

      console.log('✅ [MEDICATION QUERY] Medicamento encontrado:', data?.name);
      return data;
    },
    enabled: !!slug,
    staleTime: 15 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    onError: (error) => {
      stopLoading();
    }
  });

  useEffect(() => {
    console.log('🔍 [MEDICATION DETAILS] useEffect executado:', { medication, slug });

    if (medication) {
      console.log('💊 [MEDICATION DETAILS] Medicamento carregado:', {
        id: medication.id,
        name: medication.name,
        category_id: medication.category_id
      });

      document.title = medication.name;

      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute("content", medication.description);
      } else {
        const newMetaDescription = document.createElement('meta');
        newMetaDescription.setAttribute("name", "description");
        newMetaDescription.setAttribute("content", medication.description);
        document.head.appendChild(newMetaDescription);
      }

      console.log('🎯 [MEDICATION DETAILS] Chamando trackMedicationView...');
      trackMedicationView(
        medication.id,
        medication.name,
        medication.category_id
      );
    } else {
      console.log('⚠️ [MEDICATION DETAILS] Medicamento não encontrado ou ainda carregando');
    }
  }, [medication, trackMedicationView]);

  // Controlar loading indicator baseado no estado da query
  useEffect(() => {
    if (!isLoading && !isFetching && (medication || error)) {
      stopLoading();
    }
  }, [isLoading, isFetching, medication, error, stopLoading]);

  const { data: categories } = useMedicationCategoriesWithMedications();

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setShowScrollTop(e.currentTarget.scrollTop > 100);
  };

  const scrollToTop = () => {
    const content = document.querySelector('.medication-details');
    content?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className={getThemeClasses.pageBackground()}>
      <Header />

      <main className="flex-1 container mx-auto py-4 px-1 sm:px-4">
        <Link
          to="/"
          className="flex items-center justify-center gap-1.5 text-primary/60 hover:text-primary transition-colors mb-6 group hidden md:flex dark:text-blue-400/60 dark:hover:text-blue-400"
        >
          <Home className="w-4 h-4" />
          <span className="text-sm">Início</span>
        </Link>

        {isMobile ? (
          <MobileView
            categories={categories || []}
            currentMedicationId={medication?.id || ''}
            onMedicationSelect={(medId) => navigate(`/medicamentos/${medId}`)}
            isLoading={isLoading}
            medication={medication}
            weight={weight}
            displayWeight={displayWeight}
            setTempWeight={setTempWeight}
            setWeight={setWeight}
            age={age}
            setAge={setAge}
            slug={slug}
          />
        ) : (
          <DesktopView
            categories={categories || []}
            currentMedicationId={medication?.id || ''}
            isLoading={isLoading}
            medication={medication}
            weight={weight}
            displayWeight={displayWeight}
            setTempWeight={setTempWeight}
            setWeight={setWeight}
            age={age}
            setAge={setAge}
            slug={slug}
          />
        )}
      </main>

      <Footer />

      <Button
        variant="outline"
        size="icon"
        className={cn(
          "fixed bottom-4 right-4 rounded-full transition-all duration-300",
          "bg-gradient-to-r from-primary/20 to-primary/10 backdrop-blur-sm border-primary/20 dark:bg-gradient-to-r dark:from-blue-800/40 dark:to-blue-900/30 dark:border-blue-800/30",
          "hover:shadow-lg hover:shadow-primary/20 dark:hover:shadow-blue-800/20",
          showScrollTop ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4 pointer-events-none"
        )}
        onClick={scrollToTop}
        aria-label="Voltar ao topo da página"
      >
        <ChevronUp className="h-4 w-4" />
      </Button>
    </div>
  );
}
