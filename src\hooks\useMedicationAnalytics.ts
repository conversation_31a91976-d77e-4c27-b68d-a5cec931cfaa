import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

// <PERSON><PERSON><PERSON> ou recuperar session ID único para todo o site
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('site_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('site_session_id', sessionId);
  }
  return sessionId;
};

// Obter informações do navegador e referrer
const getBrowserInfo = () => {
  return {
    userAgent: navigator.userAgent,
    referrer: document.referrer || null,
    // IP será obtido pelo servidor
  };
};

export type AnalyticsEventType = 'medication_view' | 'page_view' | 'search' | 'calculator_use' | 'download' | 'form_submit';

interface TrackEventParams {
  eventType: AnalyticsEventType;
  medicationId?: string;
  categoryId?: string;
  page?: string;
  searchQuery?: string;
  metadata?: Record<string, any>;
}

export const useSiteAnalytics = () => {
  const trackEvent = useCallback(async ({
    eventType,
    medicationId,
    categoryId,
    page,
    searchQuery,
    metadata = {}
  }: TrackEventParams) => {
    try {
      // Tracking silencioso para melhor UX

      const sessionId = getSessionId();
      const browserInfo = getBrowserInfo();

      // Verificar se o usuário está autenticado (opcional)
      const { data: { user } } = await supabase.auth.getUser();

      // Preparar metadata expandido
      const expandedMetadata = {
        ...metadata,
        page_url: page || window.location.pathname,
        search_query: searchQuery,
        referrer: browserInfo.referrer,
        timestamp: new Date().toISOString()
      };

      const rpcParams = {
        p_session_id: sessionId,
        p_action_type: eventType,
        p_page_url: page || window.location.pathname,
        p_medication_id: medicationId || null,
        p_category_id: categoryId || null,
        p_search_query: searchQuery || null,
        p_metadata: expandedMetadata,
        p_user_agent: browserInfo.userAgent,
        p_referrer: browserInfo.referrer,
        p_ip_address: null, // Será preenchido pelo servidor
        p_user_id: user?.id || null // Null para usuários anônimos
      };

      // Chamar a função do Supabase para registrar o evento
      const { data, error } = await supabase.rpc('track_site_event', rpcParams);

      if (error) {
        // Silenciar erros de analytics para não afetar UX
      }
    } catch (error) {
      // Silenciar erros de analytics para não afetar UX
    }
  }, []);

  // Função para rastrear visualização de página
  const trackPageView = useCallback((page?: string, metadata?: Record<string, any>) => {
    trackEvent({
      eventType: 'page_view',
      page: page || window.location.pathname,
      metadata
    }).catch(() => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  // Função para rastrear visualização de medicamento (não-bloqueante)
  const trackMedicationView = useCallback((medicationId: string, medicationName?: string, categoryId?: string) => {
    trackEvent({
      eventType: 'medication_view',
      medicationId,
      categoryId,
      metadata: { medication_name: medicationName }
    }).catch(() => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  // Função para rastrear buscas
  const trackSearch = useCallback((searchQuery: string, resultsCount?: number, metadata?: Record<string, any>) => {
    trackEvent({
      eventType: 'search',
      searchQuery,
      metadata: {
        results_count: resultsCount,
        ...metadata
      }
    }).catch(() => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  // Função para rastrear uso de calculadoras
  const trackCalculatorUse = useCallback((calculatorName: string, metadata?: Record<string, any>) => {
    trackEvent({
      eventType: 'calculator_use',
      metadata: {
        calculator_name: calculatorName,
        ...metadata
      }
    }).catch(() => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  return {
    trackPageView,
    trackMedicationView,
    trackSearch,
    trackCalculatorUse,
    trackEvent
  };
};

// Manter compatibilidade com o nome antigo
export const useMedicationAnalytics = useSiteAnalytics;
