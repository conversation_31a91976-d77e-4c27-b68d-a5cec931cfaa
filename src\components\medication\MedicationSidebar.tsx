
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Grid2X2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useState, useEffect, useCallback } from "react";
import { MobileMedicationSelector } from "./MobileMedicationSelector";
import { useDebounceCallback } from "@/hooks/useDebounceCallback";

interface MedicationSidebarProps {
  categories: Array<{
    id: string;
    name: string;
    pedbook_medications: Array<{
      id: string;
      slug: string;
      name: string;
    }>;
  }> | undefined;
  currentMedicationId: string;
}

export const MedicationSidebar = ({ categories, currentMedicationId }: MedicationSidebarProps) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Helper function to normalize text for sorting (remove accents and special characters)
  const normalizeText = (text: string) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase();
  };

  // Advanced search function for medications
  const searchMedication = (medication: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeText(searchTerm);
    const normalizedMedicationName = normalizeText(medication.name);

    // Search in medication name (normalized)
    if (normalizedMedicationName.includes(normalizedSearchTerm)) {
      return true;
    }

    // Search in brands/commercial names (normalized)
    if (medication.brands) {
      const normalizedBrands = normalizeText(medication.brands);
      if (normalizedBrands.includes(normalizedSearchTerm)) {
        return true;
      }
    }

    // Search in slug
    if (medication.slug) {
      const slugifiedSearchTerm = searchTerm.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");
      if (medication.slug.includes(slugifiedSearchTerm)) {
        return true;
      }
    }

    return false;
  };

  // Log categories data for debugging (temporary)
  if (categories && searchTerm) {
    console.log('🔍 [Debug] Categories data:', categories.slice(0, 1));
    const firstCategory = categories[0];
    if (firstCategory?.pedbook_medications) {
      console.log('🔍 [Debug] First medication:', firstCategory.pedbook_medications[0]);
    }
  }

  // Filter and sort categories with advanced search
  const filteredCategories = categories
    ?.map(category => ({
      ...category,
      pedbook_medications: category.pedbook_medications?.filter(med =>
        searchMedication(med, searchTerm)
      )
    }))
    .filter(category => category.pedbook_medications?.length > 0)
    // Sort categories alphabetically by name
    .sort((a, b) => normalizeText(a.name).localeCompare(normalizeText(b.name)));



  const handleMedicationSelectInternal = useCallback((medicationId: string) => {
    // Navegar diretamente - o skeleton loading gerencia a UX
    navigate(`/medicamentos/${medicationId}`);
  }, [navigate]);

  // Debounce navigation to prevent rapid clicks
  const { debouncedCallback: handleMedicationSelect } = useDebounceCallback(
    handleMedicationSelectInternal,
    300 // 300ms debounce
  );

  if (isMobile) {
    return (
      <MobileMedicationSelector
        categories={categories}
        currentMedicationId={currentMedicationId}
        onMedicationSelect={handleMedicationSelect}
      />
    );
  }

  return (
    <aside className="w-80 bg-gradient-to-br from-white/98 via-white/95 to-blue-50/40 dark:from-slate-900/98 dark:via-slate-800/95 dark:to-slate-900/40 backdrop-blur-xl rounded-2xl shadow-2xl border-2 border-gray-300/60 dark:border-slate-600/60 p-6 h-[calc(100vh-8rem)] flex flex-col">
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
          <Input
            placeholder="Buscar por nome ou marca comercial..."
            className="pl-11 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-300/70 dark:border-gray-600/70 rounded-xl h-12 text-sm shadow-sm focus:shadow-lg focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-200"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Button
          variant="outline"
          className="w-full flex items-center gap-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-300/70 dark:border-gray-600/70 rounded-xl h-12 shadow-sm hover:shadow-lg transition-all duration-200 font-medium"
          onClick={() => {
            navigate('/medicamentos/painel');
          }}
        >
          <Grid2X2 className="h-5 w-5" />
          Todas categorias
        </Button>
      </div>

      <ScrollArea className="flex-1 pr-2 mt-6">
        <nav className="space-y-5">
          {searchTerm && filteredCategories?.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Search className="h-8 w-8 mx-auto mb-3 opacity-50" />
              <p className="text-sm">Nenhum medicamento encontrado</p>
              <p className="text-xs mt-1">Tente buscar por nome ou marca comercial</p>
            </div>
          ) : (
            filteredCategories?.map((category) => (
            <div key={category.id} className="space-y-3">
              {/* Header da categoria com bordas mais visíveis */}
              <div className="bg-gradient-to-r from-blue-500/15 to-purple-500/15 dark:from-blue-400/15 dark:to-purple-400/15 rounded-xl p-4 border-2 border-blue-200/50 dark:border-blue-700/50 shadow-sm">
                <h3 className="font-bold text-sm text-blue-800 dark:text-blue-200 uppercase tracking-wider">
                  {category.name}
                </h3>
              </div>

              {/* Lista de medicamentos */}
              <div className="space-y-2 pl-1">
                {category.pedbook_medications?.map((med) => (
                  <div
                    key={med.id}
                    className={`group relative rounded-xl border-2 transition-all duration-200 ${
                      med.id === currentMedicationId
                        ? "bg-blue-500 border-blue-500 shadow-lg"
                        : "bg-white/70 dark:bg-slate-800/70 border-gray-200/60 dark:border-gray-600/60 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50/80 dark:hover:bg-blue-900/20"
                    }`}
                  >
                    <Button
                      variant="ghost"
                      className={`w-full justify-start text-left h-auto py-3 px-4 rounded-xl border-0 ${
                        med.id === currentMedicationId
                          ? "text-white hover:bg-blue-600"
                          : "text-gray-700 dark:text-gray-300 hover:bg-transparent"
                      }`}
                      onClick={() => handleMedicationSelect(med.slug)}
                    >
                      <div className="text-xs leading-relaxed break-words">
                        <div className="font-medium">{med.name}</div>
                        {searchTerm && med.brands && normalizeText(med.brands).includes(normalizeText(searchTerm)) && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Marcas: {med.brands}
                          </div>
                        )}
                      </div>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            ))
          )}
        </nav>
      </ScrollArea>
    </aside>
  );
};
