import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Category, Formula } from "../types";
import { FormulaBrands } from "./FormulaBrands";
import { FormulaDetails } from "../FormulaDetails";

interface FormulaCategoriesProps {
  categories: Category[] | undefined;
  formulas: Formula[] | undefined;
  selectedCategory: string | null;
  setSelectedCategory: (id: string | null) => void;
  selectedFormula: Formula | null;
  setSelectedFormula: (formula: Formula | null) => void;
}

export const FormulaCategories = ({
  categories,
  formulas,
  selectedCategory,
  setSelectedCategory,
  selectedFormula,
  setSelectedFormula,
}: FormulaCategoriesProps) => {
  return (
    <div className="space-y-8">
      {/* Categories Row */}
      <div className="flex flex-wrap gap-4 justify-center">
        {categories?.map((category) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="flex-shrink-0"
          >
            <Card
              className={cn(
                "p-4 cursor-pointer transition-all duration-300 hover:shadow-lg bg-white/80 backdrop-blur-sm border-2",
                selectedCategory === category.id 
                  ? "border-primary shadow-lg transform -translate-y-1" 
                  : "border-transparent hover:-translate-y-1"
              )}
              onClick={() => {
                if (selectedCategory === category.id) {
                  setSelectedCategory(null);
                  setSelectedFormula(null);
                } else {
                  setSelectedCategory(category.id);
                  setSelectedFormula(null);
                }
              }}
            >
              <h3 className="text-lg font-medium text-gray-900 whitespace-nowrap">
                {category.name}
              </h3>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Brands Row */}
      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="pt-4"
        >
          <FormulaBrands
            formulas={formulas?.filter(f => f.category_id === selectedCategory)}
            selectedFormula={selectedFormula}
            setSelectedFormula={setSelectedFormula}
          />
        </motion.div>
      )}

      {/* Formula Details */}
      {selectedFormula && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="mt-8"
        >
          <FormulaDetails formula={selectedFormula} />
        </motion.div>
      )}
    </div>
  );
};