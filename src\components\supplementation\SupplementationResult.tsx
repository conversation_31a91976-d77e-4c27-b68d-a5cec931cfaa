
import { SupplementationResult } from "@/types/supplementation";
import { Card } from "@/components/ui/card";
import { Droplet, Sun, Pill } from "lucide-react";

interface SupplementationResultProps {
  result: SupplementationResult;
  isVisible: boolean;
}

export const SupplementationResultView = ({ result, isVisible }: SupplementationResultProps) => {
  console.log('🌙 Renderizando SupplementationResult com suporte a dark mode', { isVisible });

  if (!isVisible) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4 p-8">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 flex items-center justify-center">
            <svg 
              className="w-8 h-8 text-blue-500 dark:text-blue-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Preencha os campos com as informações necessárias para calcularmos a suplementação adequada para a criança
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="p-4 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Droplet className="h-5 w-5 text-green-600 dark:text-green-400" />
          <h3 className="font-semibold text-green-700 dark:text-green-300">Vitamina A</h3>
        </div>
        <p className="text-green-600 dark:text-green-200/90">{result.vitaminA}</p>
      </Card>
      
      <Card className="p-4 bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Sun className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          <h3 className="font-semibold text-purple-700 dark:text-purple-300">Vitamina D</h3>
        </div>
        <p className="text-purple-600 dark:text-purple-200/90">{result.vitaminD}</p>
      </Card>
      
      <Card className="p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Pill className="h-5 w-5 text-red-600 dark:text-red-400" />
          <h3 className="font-semibold text-red-700 dark:text-red-300">Ferro</h3>
        </div>
        <p className="text-red-600 dark:text-red-200/90">{result.iron}</p>
      </Card>
    </div>
  );
};
