import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarIcon, Eye, Users, Search, Calculator, Download, TrendingUp, Globe, ArrowLeft, Activity, Clock, RefreshCw } from 'lucide-react';
import { format, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { supabase } from '@/integrations/supabase/client';
import { AnalyticsChart } from '@/components/analytics/AnalyticsChart';
import { formatNumber } from '@/lib/formatNumber';
import { useNavigate } from 'react-router-dom';

interface DateRange {
  from: Date;
  to: Date;
}

export default function Analytics() {
  const navigate = useNavigate();
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 29),
    to: new Date()
  });
  const [isRealTime, setIsRealTime] = useState(true);

  // Auto-refresh para tempo real
  useEffect(() => {
    if (!isRealTime) return;

    const interval = setInterval(() => {
      // Forçar refetch das queries principais
      window.location.reload();
    }, 30000); // 30 segundos

    return () => clearInterval(interval);
  }, [isRealTime]);

  // Query para overview geral do site
  const { data: siteOverview, isLoading: siteOverviewLoading } = useQuery({
    queryKey: ["site-analytics-overview", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_site_analytics_overview', {
        start_date: startDate,
        end_date: endDate
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 10 * 1000 : 5 * 60 * 1000,
    refetchInterval: isRealTime ? 30 * 1000 : false,
  });

  // Query para páginas mais visitadas
  const { data: topPages, isLoading: topPagesLoading } = useQuery({
    queryKey: ["top-pages", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_pages', {
        start_date: startDate,
        end_date: endDate,
        page_size: 20
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para referrers
  const { data: topReferrers, isLoading: topReferrersLoading } = useQuery({
    queryKey: ["top-referrers", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_referrers', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para timeline
  const { data: timeline, isLoading: timelineLoading } = useQuery({
    queryKey: ["site-analytics-timeline", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_site_analytics_timeline', {
        start_date: startDate,
        end_date: endDate,
        interval_type: 'day'
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para buscas mais realizadas
  const { data: topSearches, isLoading: topSearchesLoading } = useQuery({
    queryKey: ["top-searches", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_searches', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para medicamentos mais visualizados
  const { data: topMedications, isLoading: topMedicationsLoading } = useQuery({
    queryKey: ["top-medications-analytics", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_medications_paginated', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15,
        page_number: 1
      });

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/dashboard')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Analytics Completo</h1>
            <p className="text-gray-600">Análise completa de tráfego e comportamento dos usuários</p>
          </div>
        </div>

        {/* Controles */}
        <div className="flex items-center gap-4">
          {/* Toggle Tempo Real */}
          <Button
            variant={isRealTime ? "default" : "outline"}
            size="sm"
            onClick={() => setIsRealTime(!isRealTime)}
            className="gap-2"
          >
            <Activity className={`h-4 w-4 ${isRealTime ? 'animate-pulse' : ''}`} />
            {isRealTime ? 'Tempo Real ON' : 'Tempo Real OFF'}
          </Button>

          {/* Seletor de Data */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                {format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} - {format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="range"
                selected={{ from: dateRange.from, to: dateRange.to }}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    setDateRange({ from: range.from, to: range.to });
                  }
                }}
                numberOfMonths={2}
                locale={ptBR}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Indicadores de Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isRealTime && (
            <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Atualizando a cada 30s
            </div>
          )}
        </div>
        
        <div className="text-xs text-muted-foreground bg-gray-50 dark:bg-gray-900/20 px-2 py-1 rounded-full">
          <Clock className="h-3 w-3 inline mr-1" />
          {format(new Date(), 'HH:mm:ss')}
        </div>
      </div>

      {/* Cards de Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Eye className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumber(siteOverview?.total_page_views || 0)}
                </div>
                <div className="text-blue-100 text-sm font-medium">Page Views</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Users className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumber(siteOverview?.unique_sessions || 0)}
                </div>
                <div className="text-green-100 text-sm font-medium">Sessões Únicas</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Search className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumber(siteOverview?.total_searches || 0)}
                </div>
                <div className="text-purple-100 text-sm font-medium">Buscas</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Calculator className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumber(siteOverview?.calculator_uses || 0)}
                </div>
                <div className="text-orange-100 text-sm font-medium">Calculadoras</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráfico de Timeline */}
      {timeline?.timeline && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Evolução do Tráfego
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AnalyticsChart
              title="Page Views por Dia"
              data={timeline.timeline.map((item: any) => ({
                name: format(new Date(item.period_date), 'dd/MM', { locale: ptBR }),
                value: item.page_views,
                sessions: item.unique_sessions,
                searches: item.searches,
                calculators: item.calculator_uses
              }))}
              type="line"
              dataKey="value"
              height={300}
              color="#3b82f6"
            />
          </CardContent>
        </Card>
      )}

      {/* Tabs com dados detalhados */}
      <Tabs defaultValue="pages" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="pages">Páginas</TabsTrigger>
          <TabsTrigger value="referrers">Referrers</TabsTrigger>
          <TabsTrigger value="searches">Buscas</TabsTrigger>
          <TabsTrigger value="medications">Medicamentos</TabsTrigger>
          <TabsTrigger value="realtime">Tempo Real</TabsTrigger>
        </TabsList>

        <TabsContent value="pages">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Páginas Mais Visitadas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPagesLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topPages?.items?.map((page: any, index: number) => (
                    <div key={page.page_url} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-bold text-blue-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{page.page_url}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumber(page.unique_visitors)} visitantes únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumber(page.page_views)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="referrers">
          <Card>
            <CardHeader>
              <CardTitle>Principais Referrers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topReferrersLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topReferrers?.items?.map((referrer: any, index: number) => (
                    <div key={referrer.referrer_domain} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-sm font-bold text-green-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{referrer.referrer_domain}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumber(referrer.unique_visitors)} visitantes únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumber(referrer.visits)}</div>
                        <div className="text-sm text-gray-500">visitas</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="searches">
          <Card>
            <CardHeader>
              <CardTitle>Buscas Mais Realizadas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topSearchesLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topSearches?.items?.map((search: any, index: number) => (
                    <div key={search.search_query} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-sm font-bold text-purple-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">"{search.search_query}"</div>
                          <div className="text-sm text-gray-500">
                            {formatNumber(search.unique_searchers)} usuários únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumber(search.search_count)}</div>
                        <div className="text-sm text-gray-500">buscas</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medications">
          <Card>
            <CardHeader>
              <CardTitle>Medicamentos Mais Visualizados</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topMedicationsLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topMedications?.items?.map((medication: any, index: number) => (
                    <div key={medication.medication_id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-sm font-bold text-red-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{medication.medication_name}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumber(medication.unique_users)} usuários únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumber(medication.view_count)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 animate-pulse" />
                Dados em Tempo Real (Últimas 24h)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {siteOverview?.total_page_views || 0}
                  </div>
                  <div className="text-sm text-blue-600">Page Views Hoje</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {siteOverview?.unique_sessions || 0}
                  </div>
                  <div className="text-sm text-green-600">Sessões Hoje</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {siteOverview?.total_searches || 0}
                  </div>
                  <div className="text-sm text-purple-600">Buscas Hoje</div>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {siteOverview?.calculator_uses || 0}
                  </div>
                  <div className="text-sm text-orange-600">Calculadoras Hoje</div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Estatísticas Adicionais</h4>
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>Bounce Rate:</span>
                    <span className="font-medium">{siteOverview?.bounce_rate || 0}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Usuários Autenticados:</span>
                    <span className="font-medium">{siteOverview?.authenticated_users || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Páginas Únicas:</span>
                    <span className="font-medium">{siteOverview?.unique_pages || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tempo Médio na Página:</span>
                    <span className="font-medium">{siteOverview?.avg_time_spent_seconds || 0}s</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
