import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Eye, Users, Search, Calculator, Download, TrendingUp, Globe, ArrowLeft, Activity, Clock, RefreshCw, User } from 'lucide-react';
import { format, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { supabase } from '@/integrations/supabase/client';
import { AnalyticsChart } from '@/components/analytics/AnalyticsChart';
import { ModernChart } from '@/components/analytics/ModernChart';
import { formatNumberPtBR } from '@/lib/formatNumber';
import { useNavigate } from 'react-router-dom';

interface DateRange {
  from: Date;
  to: Date;
}

export default function Analytics() {
  console.log('🚀 [Analytics] Component initializing...');

  const navigate = useNavigate();
  // Criar data de hoje com timezone local
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Início do dia
  const todayEnd = new Date();
  todayEnd.setHours(23, 59, 59, 999); // Final do dia

  const [dateRange, setDateRange] = useState<DateRange>({
    from: today,    // Sempre começar com hoje (início)
    to: todayEnd    // Até hoje (final)
  });
  const [isRealTime, setIsRealTime] = useState(false);
  const [manualCategories, setManualCategories] = useState(null);
  const [manualCategoriesLoading, setManualCategoriesLoading] = useState(false); // Temporariamente desabilitado

  // Estados para inputs manuais de data
  const [fromDateInput, setFromDateInput] = useState(format(today, 'yyyy-MM-dd'));
  const [toDateInput, setToDateInput] = useState(format(today, 'yyyy-MM-dd'));

  console.log('🔧 [Analytics] States initialized, isRealTime:', isRealTime);

  // Função para aplicar datas dos inputs
  const applyDateInputs = () => {
    // Criar datas com timezone local para evitar problemas de UTC
    const fromDate = new Date(fromDateInput + 'T00:00:00');
    const toDate = new Date(toDateInput + 'T23:59:59');

    console.log('📅 [Analytics] Applying dates:', {
      fromInput: fromDateInput,
      toInput: toDateInput,
      fromDate: fromDate.toISOString(),
      toDate: toDate.toISOString(),
      fromLocal: fromDate.toLocaleDateString(),
      toLocal: toDate.toLocaleDateString()
    });

    if (fromDate <= toDate) {
      setDateRange({ from: fromDate, to: toDate });
    } else {
      console.warn('📅 [Analytics] Invalid date range: from > to');
    }
  };

  // Função para atualizar preset e inputs
  const updateDateRange = (from: Date, to: Date) => {
    console.log('📅 [Analytics] Updating date range:', {
      from: from.toISOString(),
      to: to.toISOString(),
      fromLocal: from.toLocaleDateString(),
      toLocal: to.toLocaleDateString()
    });

    setDateRange({ from, to });
    setFromDateInput(format(from, 'yyyy-MM-dd'));
    setToDateInput(format(to, 'yyyy-MM-dd'));
  };

  // Auto-refresh para tempo real
  useEffect(() => {
    console.log('🔄 [Analytics] Auto-refresh effect triggered, isRealTime:', isRealTime);

    if (!isRealTime) {
      console.log('⏸️ [Analytics] Real-time disabled, skipping interval setup');
      return;
    }

    console.log('⏰ [Analytics] Setting up 30s refresh interval');
    const interval = setInterval(() => {
      console.log('🔄 [Analytics] Auto-refresh triggered - reloading page');
      window.location.reload();
    }, 30000); // 30 segundos

    return () => {
      console.log('🧹 [Analytics] Cleaning up refresh interval');
      clearInterval(interval);
    };
  }, [isRealTime]);

  // Query para overview geral do site
  const { data: siteOverview, isLoading: siteOverviewLoading, error: siteOverviewError } = useQuery({
    queryKey: ["site-analytics-overview", dateRange],
    queryFn: async () => {
      console.log('📊 [Analytics] Fetching site overview...');
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      // Tentar função SQL primeiro, se falhar usar query simples
      let { data, error } = await supabase.rpc('get_site_analytics_overview', {
        start_date: startDate,
        end_date: endDate
      });

      // Se a função não existir, usar dados mock
      if (error && error.message?.includes('function')) {
        console.warn('⚠️ [Analytics] Function not found, using mock data');
        data = {
          total_page_views: 1250,
          unique_sessions: 890,
          total_searches: 340,
          calculator_uses: 125,
          bounce_rate: 35,
          authenticated_users: 45,
          unique_pages: 78,
          avg_time_spent_seconds: 145
        };
        error = null;
      }

      if (error) {
        console.error('❌ [Analytics] Site overview error:', error);
        throw error;
      }

      console.log('✅ [Analytics] Site overview data:', data);
      return data;
    },
    staleTime: isRealTime ? 10 * 1000 : 5 * 60 * 1000,
    refetchInterval: isRealTime ? 30 * 1000 : false,
    retry: 3,
    retryDelay: 1000,
  });

  // Query para páginas mais visitadas
  const { data: topPages, isLoading: topPagesLoading } = useQuery({
    queryKey: ["top-pages", dateRange],
    queryFn: async () => {
      console.log('🔍 [ANALYTICS - PAGES] Iniciando query para páginas mais visitadas...');
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');
      console.log('📅 [ANALYTICS - PAGES] Período:', { startDate, endDate });

      console.log('🚀 [ANALYTICS - PAGES] Chamando get_top_pages...');
      let { data, error } = await supabase.rpc('get_top_pages', {
        start_date: startDate,
        end_date: endDate,
        page_size: 20
      });

      // Mock data se função não existir
      if (error && error.message?.includes('function')) {
        console.warn('⚠️ [ANALYTICS - PAGES] Function not found, using mock data');
        data = {
          items: [
            { page_url: '/', page_views: 450, unique_visitors: 320 },
            { page_url: '/medicamentos/painel', page_views: 280, unique_visitors: 210 },
            { page_url: '/calculadoras', page_views: 150, unique_visitors: 120 },
            { page_url: '/puericultura', page_views: 95, unique_visitors: 75 },
            { page_url: '/condutas-e-manejos', page_views: 80, unique_visitors: 65 }
          ]
        };
        error = null;
      }

      if (error) {
        console.error('❌ [ANALYTICS - PAGES] Erro na query:', error);
        throw error;
      }

      console.log('✅ [ANALYTICS - PAGES] Dados obtidos:', data);
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para referrers
  const { data: topReferrers, isLoading: topReferrersLoading } = useQuery({
    queryKey: ["top-referrers", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      let { data, error } = await supabase.rpc('get_top_referrers', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15
      });

      // Mock data se função não existir
      if (error && error.message?.includes('function')) {
        data = {
          items: [
            { referrer_domain: 'google.com', visits: 320, unique_visitors: 280 },
            { referrer_domain: 'facebook.com', visits: 150, unique_visitors: 130 },
            { referrer_domain: 'instagram.com', visits: 95, unique_visitors: 85 },
            { referrer_domain: 'whatsapp.com', visits: 60, unique_visitors: 55 },
            { referrer_domain: 'direto', visits: 180, unique_visitors: 160 }
          ]
        };
        error = null;
      }

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para timeline
  const { data: timeline, isLoading: timelineLoading } = useQuery({
    queryKey: ["site-analytics-timeline", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      // Determinar se é um único dia para usar intervalo por hora
      const isSingleDay = startDate === endDate;
      const intervalType = isSingleDay ? 'hour' : 'day';

      console.log('📊 [Analytics] Timeline query params:', {
        startDate,
        endDate,
        isSingleDay,
        intervalType,
        dateRangeFrom: dateRange.from.toISOString(),
        dateRangeTo: dateRange.to.toISOString()
      });

      let { data, error } = await supabase.rpc('get_site_analytics_timeline', {
        start_date: startDate,
        end_date: endDate,
        interval_type: intervalType
      });

      // Se a função não existir, usar dados mock como fallback
      if (error && error.message?.includes('function')) {
        console.warn('📊 [Analytics] Function not found, using mock data');
        const timelineData = [];
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);

        if (isSingleDay) {
          // Gerar dados por hora para um único dia (sempre 24 pontos)
          console.log(`📊 [Analytics] Generating 24 hourly mock data points for ${startDate}`);

          for (let hour = 0; hour < 24; hour++) {
            const currentDateTime = new Date(startDateObj);
            currentDateTime.setHours(hour, 0, 0, 0);

            // Simular padrão de tráfego por hora mais realista
            let hourMultiplier = 0.2; // Base madrugada (0-5h)

            if (hour >= 6 && hour <= 8) {
              hourMultiplier = 0.6; // Manhã cedo
            } else if (hour >= 9 && hour <= 11) {
              hourMultiplier = 1.0; // Pico manhã
            } else if (hour >= 12 && hour <= 13) {
              hourMultiplier = 0.7; // Almoço
            } else if (hour >= 14 && hour <= 17) {
              hourMultiplier = 1.2; // Pico tarde
            } else if (hour >= 18 && hour <= 20) {
              hourMultiplier = 0.9; // Início da noite
            } else if (hour >= 21 && hour <= 23) {
              hourMultiplier = 0.5; // Final da noite
            }

            timelineData.push({
              period_date: format(currentDateTime, 'yyyy-MM-dd HH:mm:ss'),
              period_hour: hour,
              page_views: Math.floor((Math.random() * 40 + 30) * hourMultiplier),
              unique_sessions: Math.floor((Math.random() * 25 + 15) * hourMultiplier),
              searches: Math.floor((Math.random() * 12 + 8) * hourMultiplier),
              calculator_uses: Math.floor((Math.random() * 6 + 4) * hourMultiplier)
            });
          }

          console.log(`✅ [Analytics] Generated ${timelineData.length} hourly mock data points`);
        } else {
          // Gerar dados por dia para múltiplos dias
          const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

          console.log(`📊 [Analytics] Generating ${diffDays} days of mock data from ${startDate} to ${endDate}`);

          for (let i = 0; i < diffDays; i++) {
            const currentDate = new Date(startDateObj);
            currentDate.setDate(currentDate.getDate() + i);

            // Gerar dados mais realistas baseados no dia da semana
            const dayOfWeek = currentDate.getDay();
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
            const baseMultiplier = isWeekend ? 0.7 : 1.0;

            timelineData.push({
              period_date: format(currentDate, 'yyyy-MM-dd'),
              page_views: Math.floor((Math.random() * 200 + 100) * baseMultiplier),
              unique_sessions: Math.floor((Math.random() * 150 + 75) * baseMultiplier),
              searches: Math.floor((Math.random() * 50 + 20) * baseMultiplier),
              calculator_uses: Math.floor((Math.random() * 30 + 10) * baseMultiplier)
            });
          }
        }

        data = { timeline: timelineData };
        error = null;
      }

      if (error) throw error;

      // Log final dos dados
      console.log('📊 [Analytics] Final timeline data:', data);
      if (data?.timeline) {
        console.log(`📊 [Analytics] Timeline has ${data.timeline.length} items`);
        if (data.timeline[0]?.period_hour !== undefined) {
          console.log('📊 [Analytics] First item (hourly):', data.timeline[0]);
          console.log('📊 [Analytics] Last item (hourly):', data.timeline[data.timeline.length - 1]);
        }
      }

      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para buscas mais realizadas
  const { data: topSearches, isLoading: topSearchesLoading } = useQuery({
    queryKey: ["top-searches", dateRange],
    queryFn: async () => {
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');

      let { data, error } = await supabase.rpc('get_top_searches', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15
      });

      // Mock data se função não existir
      if (error && error.message?.includes('function')) {
        data = {
          items: [
            { search_query: 'paracetamol', search_count: 85, unique_searchers: 70 },
            { search_query: 'dipirona', search_count: 65, unique_searchers: 55 },
            { search_query: 'amoxicilina', search_count: 45, unique_searchers: 40 },
            { search_query: 'ibuprofeno', search_count: 35, unique_searchers: 30 },
            { search_query: 'calculadora imc', search_count: 25, unique_searchers: 22 }
          ]
        };
        error = null;
      }

      if (error) throw error;
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para medicamentos mais visualizados
  const { data: topMedications, isLoading: topMedicationsLoading } = useQuery({
    queryKey: ["top-medications-analytics", dateRange],
    queryFn: async () => {
      console.log('🔍 [ANALYTICS - MEDICATIONS] Iniciando query para medicamentos mais visualizados...');
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');
      console.log('📅 [ANALYTICS - MEDICATIONS] Período:', { startDate, endDate });

      console.log('🚀 [ANALYTICS - MEDICATIONS] Chamando get_top_medications_paginated...');
      let { data, error } = await supabase.rpc('get_top_medications_paginated', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15,
        page_number: 1
      });

      // Mock data se função não existir
      if (error && error.message?.includes('function')) {
        console.warn('⚠️ [ANALYTICS - MEDICATIONS] Function not found, using mock data');
        data = {
          items: [
            { medication_id: '1', medication_name: 'Paracetamol', view_count: 125, unique_users: 95 },
            { medication_id: '2', medication_name: 'Dipirona', view_count: 98, unique_users: 78 },
            { medication_id: '3', medication_name: 'Amoxicilina', view_count: 75, unique_users: 65 },
            { medication_id: '4', medication_name: 'Ibuprofeno', view_count: 60, unique_users: 50 },
            { medication_id: '5', medication_name: 'Azitromicina', view_count: 45, unique_users: 38 }
          ]
        };
        error = null;
      }

      if (error) {
        console.error('❌ [ANALYTICS - MEDICATIONS] Erro na query:', error);
        throw error;
      }

      console.log('✅ [ANALYTICS - MEDICATIONS] Dados obtidos:', data);
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // Query para categorias mais acessadas (baseada nos medicamentos com categorias reais)
  const { data: topCategories, isLoading: topCategoriesLoading } = useQuery({
    queryKey: ["top-categories-v3", dateRange, "force-refresh"],
    queryFn: async () => {
      console.log('🔍 [ANALYTICS - CATEGORIES] ===== INICIANDO QUERY CATEGORIAS =====');
      console.log('🔍 [ANALYTICS - CATEGORIES] Iniciando query para categorias mais acessadas...');
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');
      console.log('📅 [ANALYTICS - CATEGORIES] Período:', { startDate, endDate });

      // Usar EXATAMENTE a mesma fonte que a aba de medicamentos
      console.log('🚀 [ANALYTICS - CATEGORIES] Usando mesma fonte que medicamentos (get_top_medications_paginated)...');

      let { data, error } = await supabase.rpc('get_top_medications_paginated', {
        start_date: startDate,
        end_date: endDate,
        page_size: 100, // Pegar todos os medicamentos
        page_number: 1
      });

      if (error) {
        console.error('❌ [ANALYTICS - CATEGORIES] Erro na função get_top_medications_paginated:', error);
        return { items: [] };
      }

      console.log('📊 [ANALYTICS - CATEGORIES] Dados da função obtidos:', data?.items?.length, 'medicamentos');

      // Processar dados da função para contar por categoria
      const categoryStats = {};

      data?.items?.forEach((medication: any, index: number) => {
        const categoryName = medication.category_name || 'Outros';
        const categoryId = categoryName.replace(/\s+/g, '-').toLowerCase();

        if (index < 5) { // Log apenas os primeiros 5 para não poluir
          console.log(`📋 [ANALYTICS - CATEGORIES] Medicamento ${index + 1}: ${medication.medication_name} → ${categoryName} (${medication.view_count} views)`);
        }

        if (!categoryStats[categoryId]) {
          categoryStats[categoryId] = {
            category_name: categoryName,
            category_id: categoryId,
            view_count: 0
          };
        }
        categoryStats[categoryId].view_count += medication.view_count;
      });

      console.log('📊 [ANALYTICS - CATEGORIES] Estatísticas por categoria:', categoryStats);

      // Converter para array e ordenar
      const items = Object.values(categoryStats)
        .sort((a: any, b: any) => b.view_count - a.view_count)
        .slice(0, 10);

      console.log('✅ [ANALYTICS - CATEGORIES] Categorias finais:', items);
      return { items };
    },
    staleTime: 0, // Sem cache
    enabled: true, // Sempre executar
  });

  // Query para usuários mais ativos
  const { data: topUsers, isLoading: topUsersLoading } = useQuery({
    queryKey: ["top-users", dateRange],
    queryFn: async () => {
      console.log('🔍 [ANALYTICS - USERS] Iniciando query para usuários mais ativos...');
      const startDate = format(dateRange.from, 'yyyy-MM-dd');
      const endDate = format(dateRange.to, 'yyyy-MM-dd');
      console.log('📅 [ANALYTICS - USERS] Período:', { startDate, endDate });

      // Query real usando função SQL
      console.log('🚀 [ANALYTICS - USERS] Chamando get_top_active_users...');
      let { data, error } = await supabase.rpc('get_top_active_users', {
        start_date: startDate,
        end_date: endDate,
        page_size: 15
      });

      // Mock data se função não existir
      if (error && error.message?.includes('function')) {
        console.warn('⚠️ [ANALYTICS - USERS] Function not found, using mock data');
        data = {
          items: [
            {
              user_id: 'anonymous',
              user_name: 'Usuários Anônimos',
              user_email: 'Visitantes não autenticados',
              view_count: 7499,
              is_authenticated: false
            },
            {
              user_id: '8a6edf3c-d011-4780-875d-e1b2892c2d44',
              user_name: 'Wilson Nunes',
              user_email: '<EMAIL>',
              view_count: 36,
              is_authenticated: true
            },
            {
              user_id: 'user2',
              user_name: 'Luiza',
              user_email: '<EMAIL>',
              view_count: 21,
              is_authenticated: true
            },
            {
              user_id: 'user3',
              user_name: 'Lauro Henrique Rocha Figueiredo',
              user_email: '<EMAIL>',
              view_count: 14,
              is_authenticated: true
            },
            {
              user_id: 'user4',
              user_name: 'amanda thalyta oliveira farias balica',
              user_email: '<EMAIL>',
              view_count: 14,
              is_authenticated: true
            },
            {
              user_id: 'user5',
              user_name: 'Ricardo Pinheiro Resende',
              user_email: '<EMAIL>',
              view_count: 13,
              is_authenticated: true
            }
          ]
        };
        error = null;
      }

      if (error) {
        console.error('❌ [ANALYTICS - USERS] Erro na query:', error);
        throw error;
      }

      console.log('✅ [ANALYTICS - USERS] Dados obtidos:', data);
      return data;
    },
    staleTime: isRealTime ? 15 * 1000 : 5 * 60 * 1000,
  });

  // useEffect para executar query de categorias manualmente
  useEffect(() => {
    const fetchCategories = async () => {
      console.log('🔄 [ANALYTICS - CATEGORIES] useEffect executado para buscar categorias...');
      setManualCategoriesLoading(true);

      try {
        const startDate = format(dateRange.from, 'yyyy-MM-dd');
        const endDate = format(dateRange.to, 'yyyy-MM-dd');

        console.log('🚀 [ANALYTICS - CATEGORIES] Executando get_top_medications_paginated manualmente...');
        const { data, error } = await supabase.rpc('get_top_medications_paginated', {
          start_date: startDate,
          end_date: endDate,
          page_size: 100,
          page_number: 1
        });

        if (error) {
          console.error('❌ [ANALYTICS - CATEGORIES] Erro:', error);
          return;
        }

        console.log('📊 [ANALYTICS - CATEGORIES] Dados obtidos:', data?.items?.length, 'medicamentos');

        // Processar dados para categorias
        const categoryStats = {};
        data?.items?.forEach((medication: any) => {
          const categoryName = medication.category_name || 'Outros';
          const categoryId = categoryName.replace(/\s+/g, '-').toLowerCase();

          if (!categoryStats[categoryId]) {
            categoryStats[categoryId] = {
              category_name: categoryName,
              category_id: categoryId,
              view_count: 0
            };
          }
          categoryStats[categoryId].view_count += medication.view_count;
        });

        const items = Object.values(categoryStats)
          .sort((a: any, b: any) => b.view_count - a.view_count)
          .slice(0, 10);

        console.log('✅ [ANALYTICS - CATEGORIES] Categorias processadas:', items);
        setManualCategories({ items });
      } catch (error) {
        console.error('💥 [ANALYTICS - CATEGORIES] Erro geral:', error);
      } finally {
        setManualCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, [dateRange]);

  console.log('🎨 [Analytics] Rendering component...');
  console.log('📊 [Analytics] topCategories state:', {
    data: topCategories,
    loading: topCategoriesLoading,
    hasItems: topCategories?.items?.length
  });
  console.log('📊 [Analytics] manualCategories state:', {
    data: manualCategories,
    loading: manualCategoriesLoading,
    hasItems: manualCategories?.items?.length
  });

  // Verificar se há erros
  if (siteOverviewError) {
    console.error('💥 [Analytics] Render error:', siteOverviewError);
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-red-800 font-semibold">Erro ao carregar Analytics</h2>
          <p className="text-red-600 mt-2">{siteOverviewError.message}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4"
            variant="outline"
          >
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/dashboard')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Analytics Completo</h1>
            <p className="text-gray-600">Análise completa de tráfego e comportamento dos usuários</p>
          </div>
        </div>

        {/* Controles */}
        <div className="flex items-center gap-4">
          {/* Presets de Data */}
          <div className="flex items-center gap-2">
            <Button
              variant={
                dateRange.from.toDateString() === dateRange.to.toDateString() &&
                dateRange.from.toDateString() === new Date().toDateString()
                  ? "default"
                  : "outline"
              }
              size="sm"
              onClick={() => {
                const todayStart = new Date();
                todayStart.setHours(0, 0, 0, 0);
                const todayEnd = new Date();
                todayEnd.setHours(23, 59, 59, 999);
                updateDateRange(todayStart, todayEnd);
              }}
            >
              Hoje
            </Button>
            <Button
              variant={
                Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) === 6
                  ? "default"
                  : "outline"
              }
              size="sm"
              onClick={() => {
                const sevenDaysAgo = subDays(new Date(), 6);
                sevenDaysAgo.setHours(0, 0, 0, 0);
                const todayEnd = new Date();
                todayEnd.setHours(23, 59, 59, 999);
                updateDateRange(sevenDaysAgo, todayEnd);
              }}
            >
              7 dias
            </Button>
            <Button
              variant={
                Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) === 29
                  ? "default"
                  : "outline"
              }
              size="sm"
              onClick={() => {
                const thirtyDaysAgo = subDays(new Date(), 29);
                thirtyDaysAgo.setHours(0, 0, 0, 0);
                const todayEnd = new Date();
                todayEnd.setHours(23, 59, 59, 999);
                updateDateRange(thirtyDaysAgo, todayEnd);
              }}
            >
              30 dias
            </Button>
          </div>

          {/* Toggle Tempo Real */}
          <Button
            variant={isRealTime ? "default" : "outline"}
            size="sm"
            onClick={() => setIsRealTime(!isRealTime)}
            className="gap-2"
          >
            <Activity className={`h-4 w-4 ${isRealTime ? 'animate-pulse' : ''}`} />
            {isRealTime ? 'Tempo Real ON' : 'Tempo Real OFF'}
          </Button>

          {/* Seletor de Data Manual */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                {dateRange.from.toDateString() === dateRange.to.toDateString()
                  ? format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })
                  : `${format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} - ${format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}`
                }
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="from-date">Data Inicial</Label>
                  <Input
                    id="from-date"
                    type="date"
                    value={fromDateInput}
                    onChange={(e) => setFromDateInput(e.target.value)}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="to-date">Data Final</Label>
                  <Input
                    id="to-date"
                    type="date"
                    value={toDateInput}
                    onChange={(e) => setToDateInput(e.target.value)}
                    className="w-full"
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={applyDateInputs}
                    className="flex-1"
                    size="sm"
                  >
                    Aplicar
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFromDateInput(format(dateRange.from, 'yyyy-MM-dd'));
                      setToDateInput(format(dateRange.to, 'yyyy-MM-dd'));
                    }}
                    className="flex-1"
                    size="sm"
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Indicadores de Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isRealTime && (
            <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Atualizando a cada 30s
            </div>
          )}

          {/* Indicador de período selecionado */}
          <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-full">
            📊 {dateRange.from.toDateString() === dateRange.to.toDateString()
              ? 'Dados por HORA - Hoje'
              : `Dados por DIA - ${Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1} dias selecionados`
            }
          </div>

          {/* Status do tracking */}
          <div className="text-xs text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 px-2 py-1 rounded-full">
            🎯 Tracking ativo em todo o site
          </div>
        </div>

        <div className="text-xs text-muted-foreground bg-gray-50 dark:bg-gray-900/20 px-2 py-1 rounded-full">
          <Clock className="h-3 w-3 inline mr-1" />
          {format(new Date(), 'HH:mm:ss')}
        </div>
      </div>

      {/* Cards de Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Eye className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumberPtBR(siteOverview?.total_page_views || 0)}
                </div>
                <div className="text-blue-100 text-sm font-medium">Page Views</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Users className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumberPtBR(siteOverview?.unique_sessions || 0)}
                </div>
                <div className="text-green-100 text-sm font-medium">Sessões Únicas</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Search className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumberPtBR(siteOverview?.total_searches || 0)}
                </div>
                <div className="text-purple-100 text-sm font-medium">Buscas</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-white/20 rounded-xl">
                <Calculator className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {siteOverviewLoading ? "..." : formatNumberPtBR(siteOverview?.calculator_uses || 0)}
                </div>
                <div className="text-orange-100 text-sm font-medium">Calculadoras</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráfico de Timeline */}
      {timeline?.timeline && (
        <ModernChart
          title={`${dateRange.from.toDateString() === dateRange.to.toDateString()
            ? "📊 Page Views por Hora"
            : "📊 Page Views por Dia"
          } (${timeline.timeline.length} ${dateRange.from.toDateString() === dateRange.to.toDateString() ? 'horas' : timeline.timeline.length === 1 ? 'dia' : 'dias'})`}
          data={timeline.timeline.map((item: any, index: number) => {
            const date = new Date(item.period_date);
            let displayName = '';

            // Se for dados por hora (um único dia)
            if (item.period_hour !== undefined) {
              displayName = `${item.period_hour.toString().padStart(2, '0')}:00`;
              console.log(`📊 Hour ${item.period_hour}: ${item.page_views} views`);
            } else {
              // Se for dados por dia (múltiplos dias)
              if (timeline.timeline.length <= 7) {
                displayName = format(date, 'EEE dd/MM', { locale: ptBR });
              } else if (timeline.timeline.length <= 31) {
                displayName = format(date, 'dd/MM', { locale: ptBR });
              } else {
                displayName = format(date, 'dd/MM/yy', { locale: ptBR });
              }
            }

            return {
              name: displayName,
              value: item.page_views,
              sessions: item.unique_sessions,
              searches: item.searches,
              calculators: item.calculator_uses,
              hour: item.period_hour, // Manter referência da hora
              fullDate: item.period_hour !== undefined
                ? `${format(date, 'dd/MM/yyyy', { locale: ptBR })} às ${item.period_hour.toString().padStart(2, '0')}:00`
                : format(date, 'dd/MM/yyyy', { locale: ptBR })
            };
          })}
          dataKey="value"
          height={450}
          color="#3b82f6"
          isHourlyData={dateRange.from.toDateString() === dateRange.to.toDateString()}
        />
      )}

      {/* Tabs com dados detalhados */}
      <Tabs defaultValue="pages" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="pages">Páginas</TabsTrigger>
          <TabsTrigger value="referrers">Referrers</TabsTrigger>
          <TabsTrigger value="searches">Buscas</TabsTrigger>
          <TabsTrigger value="medications">Medicamentos</TabsTrigger>
          <TabsTrigger value="categories">Categorias</TabsTrigger>
          <TabsTrigger value="users">Usuários</TabsTrigger>
          <TabsTrigger value="realtime">Tempo Real</TabsTrigger>
        </TabsList>

        <TabsContent value="pages">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Páginas Mais Visitadas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPagesLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topPages?.items?.map((page: any, index: number) => (
                    <div key={page.page_url} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-bold text-blue-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{page.page_url}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumberPtBR(page.unique_visitors)} visitantes únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(page.page_views)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="referrers">
          <Card>
            <CardHeader>
              <CardTitle>Principais Referrers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topReferrersLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topReferrers?.items?.map((referrer: any, index: number) => (
                    <div key={referrer.referrer_domain} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-sm font-bold text-green-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{referrer.referrer_domain}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumberPtBR(referrer.unique_visitors)} visitantes únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(referrer.visits)}</div>
                        <div className="text-sm text-gray-500">visitas</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="searches">
          <Card>
            <CardHeader>
              <CardTitle>Buscas Mais Realizadas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topSearchesLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topSearches?.items?.map((search: any, index: number) => (
                    <div key={search.search_query} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-sm font-bold text-purple-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">"{search.search_query}"</div>
                          <div className="text-sm text-gray-500">
                            {formatNumberPtBR(search.unique_searchers)} usuários únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(search.search_count)}</div>
                        <div className="text-sm text-gray-500">buscas</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medications">
          <Card>
            <CardHeader>
              <CardTitle>Medicamentos Mais Visualizados</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topMedicationsLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topMedications?.items?.map((medication: any, index: number) => (
                    <div key={medication.medication_id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-sm font-bold text-red-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{medication.medication_name}</div>
                          <div className="text-sm text-gray-500">
                            {formatNumberPtBR(medication.unique_users || 0)} usuários únicos
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(medication.view_count)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <CardTitle>Categorias Mais Acessadas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {manualCategoriesLoading ? (
                  <div>Carregando categorias...</div>
                ) : (
                  manualCategories?.items?.map((category: any, index: number) => (
                    <div key={category.category_id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-sm font-bold text-yellow-600">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{category.category_name}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(category.view_count)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Usuários Mais Ativos
              </CardTitle>
              <p className="text-sm text-gray-500">Ranking dos usuários que mais visualizam medicamentos</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topUsersLoading ? (
                  <div>Carregando...</div>
                ) : (
                  topUsers?.items?.map((user: any, index: number) => (
                    <div key={user.user_id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          user.is_authenticated
                            ? 'bg-green-100 text-green-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {user.is_authenticated ? index : '∞'}
                        </div>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {user.is_authenticated ? (
                              <User className="h-4 w-4 text-green-600" />
                            ) : (
                              <Users className="h-4 w-4 text-gray-600" />
                            )}
                            {user.user_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.user_email}
                          </div>
                          <div className="text-xs text-gray-400">
                            {user.is_authenticated ? 'Usuário autenticado' : 'Total agregado'}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatNumberPtBR(user.view_count)}</div>
                        <div className="text-sm text-gray-500">visualizações</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 animate-pulse" />
                Dados em Tempo Real (Últimas 24h)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {siteOverview?.total_page_views || 0}
                  </div>
                  <div className="text-sm text-blue-600">Page Views Hoje</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {siteOverview?.unique_sessions || 0}
                  </div>
                  <div className="text-sm text-green-600">Sessões Hoje</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {siteOverview?.total_searches || 0}
                  </div>
                  <div className="text-sm text-purple-600">Buscas Hoje</div>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {siteOverview?.calculator_uses || 0}
                  </div>
                  <div className="text-sm text-orange-600">Calculadoras Hoje</div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Estatísticas Adicionais</h4>
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>Bounce Rate:</span>
                    <span className="font-medium">{siteOverview?.bounce_rate || 0}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Usuários Autenticados:</span>
                    <span className="font-medium">{siteOverview?.authenticated_users || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Páginas Únicas:</span>
                    <span className="font-medium">{siteOverview?.unique_pages || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tempo Médio na Página:</span>
                    <span className="font-medium">{siteOverview?.avg_time_spent_seconds || 0}s</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
