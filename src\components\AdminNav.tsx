
import { NavLink } from "react-router-dom";
import { useEffect, useState } from "react";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { LayoutDashboard, Shield, Users, PlusCircle, Baby, BarChart3 } from "lucide-react";
import "tailwindcss/tailwind.css";

export default function AdminNav() {
  const supabase = useSupabaseClient();
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setIsLoading(true);

        // Verificar se é super admin
        const { data: superAdminRoleData, error: superAdminRoleError } = await supabase
          .from("admin_roles")
          .select("id, name")
          .eq("name", "super_admin")
          .maybeSingle();

        if (superAdminRoleError) throw superAdminRoleError;

        const superAdminRoleId = superAdminRoleData?.id;

        if (!superAdminRoleId) {
          setUserPermissions(["all"]);
          setIsLoading(false);
          return;
        }

        const { data: roleData, error: roleError } = await supabase
          .from("admin_user_roles")
          .select("role_id");

        if (roleError) throw roleError;

        const isSuperAdmin = roleData.some(role => role.role_id === superAdminRoleId);
        setIsSuperAdmin(isSuperAdmin);

        if (isSuperAdmin) {
          setUserPermissions(["all"]);
          setIsLoading(false);
          return;
        }

        try {
          // Buscar permissões específicas
          const { data: permissionsData, error: permissionsError } = await supabase
            .from("admin_user_permissions")
            .select("resource");

          if (permissionsError) throw permissionsError;

          const permissions = permissionsData.map(p => p.resource);
          setUserPermissions(['dashboard', ...permissions]);
        } catch (error) {
          setUserPermissions(["all"]);
        }
      } catch (error) {
        setUserPermissions(["dashboard"]);
      } finally {
        setIsLoading(false);
      }
    };

    checkPermissions();
  }, [supabase]);

  const hasPermission = (module: string) => {
    if (isLoading) return false;
    if (isSuperAdmin) return true;
    if (userPermissions.includes("all")) return true;
    return userPermissions.includes(module);
  };



  return (
    <nav className="bg-white border-b shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center h-12 overflow-x-auto">
          <NavLink
            to="/admin/dashboard"
            className={({ isActive }) =>
              `inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                isActive
                  ? "border-primary bg-primary/10 text-primary"
                  : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
              }`
            }
          >
            <LayoutDashboard className="h-4 w-4" />
            <span>Dashboard</span>
          </NavLink>

          {hasPermission("admin-users") && (
            <NavLink
              to="/admin/admin-users"
              className={({ isActive }) =>
                `ml-2 inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                  isActive
                    ? "border-primary bg-primary/10 text-primary"
                    : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
                }`
              }
            >
              <Users className="h-4 w-4" />
              <span>Administradores</span>
            </NavLink>
          )}

          {/* Add the new Drug Interaction Medications link */}
          {hasPermission("all") && (
            <NavLink
              to="/admin/drug-interaction-medications"
              className={({ isActive }) =>
                `ml-2 inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                  isActive
                    ? "border-primary bg-primary/10 text-primary"
                    : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
                }`
              }
            >
              <PlusCircle className="h-4 w-4" />
              <span>Medicamentos para Interações</span>
            </NavLink>
          )}

          {/* Add the new Breastfeeding Medications link */}
          {hasPermission("all") && (
            <NavLink
              to="/admin/breastfeeding-medications"
              className={({ isActive }) =>
                `ml-2 inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                  isActive
                    ? "border-primary bg-primary/10 text-primary"
                    : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
                }`
              }
            >
              <Baby className="h-4 w-4" />
              <span>Medicamentos e Amamentação</span>
            </NavLink>
          )}

          {/* Analytics Links */}
          {hasPermission("all") && (
            <>
              <NavLink
                to="/admin/site-analytics"
                className={({ isActive }) =>
                  `ml-2 inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                    isActive
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
                  }`
                }
              >
                <BarChart3 className="h-4 w-4" />
                <span>Analytics do Site</span>
              </NavLink>

              <NavLink
                to="/admin/medication-analytics"
                className={({ isActive }) =>
                  `ml-2 inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium gap-2 ${
                    isActive
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-gray-200 text-gray-600 hover:text-primary hover:bg-primary/5"
                  }`
                }
              >
                <BarChart3 className="h-4 w-4" />
                <span>Analytics de Medicamentos</span>
              </NavLink>
            </>
          )}



          {/* Outros módulos podem ser adicionados aqui, seguindo o mesmo padrão */}
        </div>
      </div>
    </nav>
  );
}
