import { Maturity, SupplementationInput, SupplementationResult } from "@/types/supplementation";

export const calculateVitaminA = (ageInDays: number): string => {
  if (ageInDays > 1825) {
    return 'Suplementação de Vitamina A não está mais indicada após 5 anos de idade.';
  }
  if (ageInDays >= 183 && ageInDays <= 365) {
    return 'Uma dose de 100.000 UI de vitamina A deve ser administrada a partir dos 6 meses de idade em crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com condições que afetam a absorção de nutrientes.';
  }
  if (ageInDays >= 366 && ageInDays <= 1825) {
    if ((ageInDays - 366) % (6 * 30) == 0) {
      return '200.000 UI';
    }
    return 'Uma dose de 200.000 UI de vitamina A deve ser administrada a cada 6 meses, a partir dos 12 meses de idade, até os 5 anos, para crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com condições que afetam a absorção de nutrientes.';
  }
  return 'Suplementação de Vitamina A deve ser iniciada aos 6 meses em crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com doenças que afetam a absorção de nutrientes.';
};

export const calculateVitaminD = (ageInDays: number): string => {
  if (ageInDays >= 0 && ageInDays <= 365) {
    return '2 gotas (400 UI), todos os dias, até 1 ano de idade.';
  }
  if (ageInDays >= 366 && ageInDays <= 730) {
    return '3 gotas (600 UI), todos os dias, até 2 anos de idade.';
  }
  return 'Suplementação de Vitamina D não está mais indicada após 2 anos de idade.';
};

export const calculateIron = (
  ageInDays: number,
  currentWeight: number,
  birthWeight: number,
  maturity: Maturity,
  exclusiveBreastfeeding: boolean,
  hasRiskFactors: boolean
): string => {
  if (ageInDays > 730) {
    return 'Suplementação de ferro não está mais indicada após 2 anos de idade.';
  }

  let dosePerKg = 0;

  if (hasRiskFactors) {
    if (maturity == 'Term' && birthWeight >= 2500) {
      if (ageInDays < 90) {
        return 'Suplementação de ferro deve ser iniciada aos 90 dias de vida devido aos fatores de risco.';
      }
      dosePerKg = 1;
      // currentWeight já está em gramas, precisamos converter para kg
      return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
    }

    if (birthWeight > 1500) {
      if (ageInDays < 30) {
        return 'Suplementação de ferro deve ser iniciada com 30 dias de vida devido aos fatores de risco.';
      }
      dosePerKg = 2;
      return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
    }

    if (birthWeight >= 1000 && birthWeight <= 1500) {
      if (ageInDays < 30) {
        return 'Suplementação de ferro deve ser iniciada com 30 dias de vida devido aos fatores de risco.';
      }
      dosePerKg = 3;
      return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
    }

    if (birthWeight < 1000) {
      if (ageInDays < 30) {
        return 'Suplementação de ferro deve ser iniciada com 30 dias de vida devido aos fatores de risco.';
      }
      dosePerKg = 4;
      return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
    }

    return 'Situação não contemplada nas recomendações';
  }

  if (exclusiveBreastfeeding && birthWeight >= 2500 && maturity === 'Term') {
    if (ageInDays < 180) {
      return 'A suplementação de ferro deve ser iniciada aos 180 dias de vida para lactentes a termo e com peso adequado ao nascer, que estão em aleitamento materno exclusivo.';
    }
    dosePerKg = 1;
    return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
  }

  if (ageInDays > 365) {
    dosePerKg = 1;
    return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
  }

  if (maturity == 'Term') {
    if (birthWeight >= 2500) {
      if (ageInDays < 90) {
        return 'Suplementação de ferro deve ser iniciada aos 90 dias de vida.';
      }
      dosePerKg = 1;
      return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
    }

    if (ageInDays < 30) {
      return 'Suplementação de ferro deve ser iniciada com 30 dias de vida.';
    }
    dosePerKg = 2;
    return `${dosePerKg * (currentWeight / 1000)} mg de ferro elementar/dia`;
  }

  return 'Situação não contemplada nas recomendações';
};

export const calculateSupplementation = (input: SupplementationInput): SupplementationResult => {
  return {
    vitaminA: calculateVitaminA(input.ageInDays),
    vitaminD: calculateVitaminD(input.ageInDays),
    iron: calculateIron(
      input.ageInDays,
      input.currentWeight,
      input.birthWeight,
      input.maturity,
      input.exclusiveBreastfeeding,
      input.hasRiskFactors
    ),
  };
};