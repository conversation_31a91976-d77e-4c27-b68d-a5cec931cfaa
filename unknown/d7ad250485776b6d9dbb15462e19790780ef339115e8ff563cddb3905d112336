import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogClose
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Check, Search, Trash2, X, HelpCircle, CheckCircle, Info } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import type { StudyTopic } from "@/types/study-schedule";
import type { FilterOption } from "@/components/filters/types";
import { useUserStudyStats } from "@/hooks/useUserStudyStats";
import { toast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { DeleteTopicDialog } from "./DeleteTopicDialog";
import { isValidTimeFormat } from "@/utils/formatTime";

interface TopicEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic;
  onSave: (updatedTopic: StudyTopic) => void;
  onMarkStudied?: (topicId: string, revisionNumber?: number) => void;
  categories: FilterOption[];
  scheduleId: string;
  isCreating?: boolean;
  isManual?: boolean;
}

export const TopicEditDialog = ({
  open,
  onOpenChange,
  topic,
  onSave,
  categories,
  scheduleId,
  isCreating = false,
  isManual = false,
}: TopicEditDialogProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialty, setSelectedSpecialty] = useState(topic.specialty);
  const [selectedTheme, setSelectedTheme] = useState(topic.theme);
  const [selectedFocus, setSelectedFocus] = useState(topic.focus);
  const [startTime, setStartTime] = useState(topic.startTime);
  const [duration, setDuration] = useState(topic.duration);
  const [activity, setActivity] = useState(topic.activity);
  const [activeTab, setActiveTab] = useState<"topic" | "time" | "help">("topic");
  const [step, setStep] = useState(1);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [showCustomDuration, setShowCustomDuration] = useState(false);
  const [customDuration, setCustomDuration] = useState("");

  const [manualSpecialty, setManualSpecialty] = useState(topic.specialty || "");
  const [manualTheme, setManualTheme] = useState(topic.theme || "");
  const [manualFocus, setManualFocus] = useState(topic.focus || "");

  const [expandedSection, setExpandedSection] = useState<"specialty" | "theme" | "focus">("specialty");

  console.log("🔄 Renderizando TopicEditDialog, estado:", { open, isManual });

  useEffect(() => {
    if (topic) {
      setSelectedSpecialty(topic.specialty);
      setSelectedTheme(topic.theme);
      setSelectedFocus(topic.focus);
      setStartTime(topic.startTime);
      setDuration(topic.duration);
      setActivity(topic.activity);
      
      setManualSpecialty(topic.specialty || "");
      setManualTheme(topic.theme || "");
      setManualFocus(topic.focus || "");
    }
  }, [topic]);

  const handleDeleteComplete = () => {
    console.log("✅ Delete completed, closing dialog");
    onOpenChange(false);
  };

  const topicWithScheduleId = {
    ...topic,
    schedule_id: scheduleId,
  };

  const { data: studyStats, isLoading: isLoadingStats } = useUserStudyStats();

  const specialties = categories.filter((cat) => cat.type === "specialty");
  const themes = categories.filter(
    (cat) =>
      cat.type === "theme" &&
      cat.parentId === specialties.find((s) => s.name === selectedSpecialty)?.id
  );
  const focuses = categories.filter(
    (cat) =>
      cat.type === "focus" &&
      cat.parentId === themes.find((t) => t.name === selectedTheme)?.id
  );

  const filteredSpecialties = specialties.filter(
    (specialty) => specialty.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredThemes = themes.filter(
    (theme) => theme.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFocuses = focuses.filter(
    (focus) => focus.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    console.log("👀 Expandindo seção:", expandedSection);
    console.log("🔢 Etapa atual:", step);
    
    if (expandedSection === "specialty" && selectedSpecialty && !selectedTheme) {
      setExpandedSection("theme");
    } else if (expandedSection === "theme" && selectedTheme && !selectedFocus) {
      setExpandedSection("focus");
    }
  }, [selectedSpecialty, selectedTheme, selectedFocus, expandedSection]);

  const isItemStudied = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ): boolean => {
    if (!studyStats) return false;

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    return statsMap.some((stat) => stat.name === name);
  };

  const getStatsText = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    if (!studyStats) return "";

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    const stats = statsMap.find((s) => s.name === name);
    if (!stats) return "";

    return ` (${stats.correct}/${stats.total})`;
  };

  const getBadgeStyle = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    const studied = isItemStudied(name, type);
    return studied
      ? "bg-green-100 text-green-800 hover:bg-green-200"
      : "bg-blue-100 text-blue-800 hover:bg-blue-200";
  };

  const getStatusEmoji = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    const studied = isItemStudied(name, type);
    return studied ? "✅" : "🆕";
  };

  const handleSave = () => {
    console.log("💾 Salvando tópico...");
    
    let finalDuration = duration;
    if (showCustomDuration && customDuration) {
      if (isValidTimeFormat(customDuration)) {
        finalDuration = customDuration;
      } else {
        toast({
          title: "Formato inválido",
          description: "Use o formato H:MM (ex: 1:30 para 1h e 30min)",
          variant: "destructive",
        });
        return;
      }
    }
    
    if (isManual) {
      if (!manualSpecialty || !manualTheme || !manualFocus) {
        toast({
          title: "Campos obrigatórios",
          description: "Por favor, preencha todos os campos.",
          variant: "destructive",
        });
        return;
      }

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId,
        specialty: manualSpecialty,
        theme: manualTheme,
        focus: manualFocus,
        startTime,
        duration: finalDuration,
        activity,
        is_manual: true,
      };

      console.log("✅ Tópico manual atualizado:", updatedTopic);
      onSave(updatedTopic);
      onOpenChange(false);
    } else {
      if (!selectedSpecialty || !selectedTheme || !selectedFocus) {
        toast({
          title: "Campos obrigatórios",
          description: "Por favor, selecione especialidade, tema e foco.",
          variant: "destructive",
        });
        return;
      }
      
      const selectedSpecialtyObj = specialties.find(
        (s) => s.name === selectedSpecialty
      );
      const selectedThemeObj = themes.find((t) => t.name === selectedTheme);
      const selectedFocusObj = focuses.find((f) => f.name === selectedFocus);

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId,
        specialty: selectedSpecialty,
        specialtyId: selectedSpecialtyObj?.id,
        theme: selectedTheme,
        themeId: selectedThemeObj?.id,
        focus: selectedFocus,
        focusId: selectedFocusObj?.id,
        startTime,
        duration: finalDuration,
        activity,
        is_manual: false,
      };

      console.log("✅ Tópico da plataforma atualizado:", updatedTopic);
      onSave(updatedTopic);
      onOpenChange(false);
    }
  };

  const nextStep = () => {
    if (step < 3) {
      setStep(step + 1);
      if (step + 1 === 2) {
        setActiveTab("time");
      } else if (step + 1 === 3) {
        setActiveTab("help");
      }
    } else {
      handleSave();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
      if (step - 1 === 1) {
        setActiveTab("topic");
      } else if (step - 1 === 2) {
        setActiveTab("time");
      }
    }
  };

  const getDialogTitle = () => {
    if (isCreating) {
      return `${step}/3: ${isManual ? "Criando Tópico Manual" : "Selecionando Tópico da Plataforma"}`;
    } else {
      return "Editar Tópico de Estudo";
    }
  };

  const canContinue = () => {
    if (step === 1) {
      return isManual 
        ? !!manualSpecialty && !!manualTheme && !!manualFocus 
        : !!selectedSpecialty && !!selectedTheme && !!selectedFocus;
    }
    if (step === 2) {
      if (showCustomDuration && customDuration) {
        return !!startTime && isValidTimeFormat(customDuration);
      }
      return !!startTime && !!duration;
    }
    return true;
  };

  const handleDurationSelect = (value: string) => {
    setDuration(value);
    setShowCustomDuration(false);
  };

  const handleCustomDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomDuration(e.target.value);
    if (isValidTimeFormat(e.target.value)) {
      setDuration(e.target.value);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md w-[calc(100%-2rem)] p-0 gap-0 overflow-hidden max-h-[90vh] flex flex-col" hideCloseButton>
        <DialogHeader className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-6 relative">
          <DialogTitle className="text-xl font-bold text-center">{getDialogTitle()}</DialogTitle>
          <DialogDescription className="text-white/90 text-center mt-1">
            {step === 1 && (isManual 
              ? "Complete os detalhes do seu tópico personalizado" 
              : "Selecione um tema específico para estudar")}
            {step === 2 && "Configure quando e quanto tempo deseja estudar"}
            {step === 3 && "Revise e confirme seu tópico de estudo"}
          </DialogDescription>
          <DialogClose className="absolute right-4 top-4 rounded-full bg-white/20 p-1 opacity-70 hover:opacity-100">
            <X className="h-4 w-4" />
          </DialogClose>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="flex-1 flex flex-col">
            <TabsList className="flex-none grid w-full grid-cols-3 rounded-none border-b">
              <TabsTrigger value="topic" disabled={step !== 1}>
                1. Tópico
              </TabsTrigger>
              <TabsTrigger value="time" disabled={step !== 2}>
                2. Horário
              </TabsTrigger>
              <TabsTrigger value="help" disabled={step !== 3}>
                3. Revisão
              </TabsTrigger>
            </TabsList>

            <TabsContent value="topic" className="flex-1 p-0">
              {isManual ? (
                <div className="p-6 space-y-4">
                  <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-6 flex items-start gap-3">
                    <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <p className="text-sm text-blue-700 break-words">
                      Crie seu próprio tópico personalizado com detalhes específicos. Ideal para temas que não estão disponíveis na plataforma.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="specialty" className="text-base font-medium">Especialidade</Label>
                      <Input
                        id="specialty"
                        value={manualSpecialty}
                        onChange={(e) => setManualSpecialty(e.target.value)}
                        placeholder="Ex: Pediatria"
                        className="border-2 focus:border-green-500"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="theme" className="text-base font-medium">Tema</Label>
                      <Input
                        id="theme"
                        value={manualTheme}
                        onChange={(e) => setManualTheme(e.target.value)}
                        placeholder="Ex: Doenças Respiratórias"
                        className="border-2 focus:border-green-500"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="focus" className="text-base font-medium">Foco</Label>
                      <Input
                        id="focus"
                        value={manualFocus}
                        onChange={(e) => setManualFocus(e.target.value)}
                        placeholder="Ex: Asma na Infância"
                        className="border-2 focus:border-green-500"
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col h-full">
                  <div className="flex-none p-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Buscar especialidade, tema ou foco..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9 border-2 focus:border-green-500"
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-100 rounded-lg mx-4 p-4 mb-4 flex items-start gap-3">
                    <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <p className="text-sm text-blue-700 break-words">
                      Escolha um tópico da plataforma para estudar. Selecione primeiro a especialidade, depois o tema e por fim o foco específico.
                    </p>
                  </div>

                  <div className="p-4 space-y-6">
                    <div className={`border rounded-lg p-4 bg-white ${expandedSection === "specialty" ? "border-green-500" : ""}`}>
                      <Button 
                        variant="ghost" 
                        className="w-full flex justify-between items-center mb-2"
                        onClick={() => setExpandedSection("specialty")}
                      >
                        <h3 className="font-semibold flex items-center text-left truncate max-w-full">
                          <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0">1</span>
                          <span className="truncate">Especialidade</span>
                          {selectedSpecialty && (
                            <span className="ml-2 text-green-600 truncate max-w-[150px]">({selectedSpecialty})</span>
                          )}
                        </h3>
                        <span className="text-gray-400 flex-shrink-0">{expandedSection === "specialty" ? "▼" : "▶"}</span>
                      </Button>
                      
                      {expandedSection === "specialty" && (
                        <ScrollArea className="h-[200px] w-full mt-2">
                          <div className="space-y-1 pr-4">
                            {filteredSpecialties.map((specialty) => (
                              <Button
                                key={specialty.id}
                                variant={selectedSpecialty === specialty.name ? "secondary" : "ghost"}
                                className="w-full justify-start mb-1"
                                onClick={() => {
                                  console.log("🔍 Especialidade selecionada:", specialty.name);
                                  setSelectedSpecialty(specialty.name);
                                  setSelectedTheme("");
                                  setSelectedFocus("");
                                  setExpandedSection("theme");
                                }}
                              >
                                {selectedSpecialty === specialty.name && (
                                  <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                                )}
                                <span className="truncate text-left">{specialty.name}</span>
                                <Badge
                                  className={`ml-2 ${getBadgeStyle(
                                    specialty.name,
                                    "specialty"
                                  )}`}
                                >
                                  {getStatusEmoji(specialty.name, "specialty")}
                                  {getStatsText(specialty.name, "specialty")}
                                </Badge>
                              </Button>
                            ))}
                          </div>
                        </ScrollArea>
                      )}
                    </div>

                    {selectedSpecialty && (
                      <div className={`border rounded-lg p-4 bg-white ${expandedSection === "theme" ? "border-green-500" : ""}`}>
                        <Button 
                          variant="ghost" 
                          className="w-full flex justify-between items-center mb-2"
                          onClick={() => setExpandedSection("theme")}
                          disabled={!selectedSpecialty}
                        >
                          <h3 className="font-semibold flex items-center text-left truncate max-w-full">
                            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0">2</span>
                            <span className="truncate">Tema</span>
                            {selectedTheme && (
                              <span className="ml-2 text-green-600 truncate max-w-[150px]">({selectedTheme})</span>
                            )}
                          </h3>
                          <span className="text-gray-400 flex-shrink-0">{expandedSection === "theme" ? "▼" : "▶"}</span>
                        </Button>
                        
                        {expandedSection === "theme" && (
                          <ScrollArea className="h-[200px] w-full mt-2">
                            <div className="space-y-1 pr-4">
                              {filteredThemes.length === 0 ? (
                                <p className="text-gray-500 text-sm py-2">Nenhum tema encontrado para esta especialidade.</p>
                              ) : (
                                filteredThemes.map((theme) => (
                                  <Button
                                    key={theme.id}
                                    variant={selectedTheme === theme.name ? "secondary" : "ghost"}
                                    className="w-full justify-start mb-1"
                                    onClick={() => {
                                      console.log("🔍 Tema selecionado:", theme.name);
                                      setSelectedTheme(theme.name);
                                      setSelectedFocus("");
                                      setExpandedSection("focus");
                                    }}
                                  >
                                    {selectedTheme === theme.name && (
                                      <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                                    )}
                                    <span className="truncate text-left">{theme.name}</span>
                                    <Badge
                                      className={`ml-2 ${getBadgeStyle(
                                        theme.name,
                                        "theme"
                                      )}`}
                                    >
                                      {getStatusEmoji(theme.name, "theme")}
                                      {getStatsText(theme.name, "theme")}
                                    </Badge>
                                  </Button>
                                ))
                              )}
                            </div>
                          </ScrollArea>
                        )}
                      </div>
                    )}

                    {selectedTheme && (
                      <div className={`border rounded-lg p-4 bg-white ${expandedSection === "focus" ? "border-green-500" : ""}`}>
                        <Button 
                          variant="ghost" 
                          className="w-full flex justify-between items-center mb-2"
                          onClick={() => setExpandedSection("focus")}
                          disabled={!selectedTheme}
                        >
                          <h3 className="font-semibold flex items-center text-left truncate max-w-full">
                            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0">3</span>
                            <span className="truncate">Foco</span>
                            {selectedFocus && (
                              <span className="ml-2 text-green-600 truncate max-w-[150px]">({selectedFocus})</span>
                            )}
                          </h3>
                          <span className="text-gray-400 flex-shrink-0">{expandedSection === "focus" ? "▼" : "▶"}</span>
                        </Button>
                        
                        {expandedSection === "focus" && (
                          <ScrollArea className="h-[200px] w-full mt-2">
                            <div className="space-y-1 pr-4">
                              {filteredFocuses.length === 0 ? (
                                <p className="text-gray-500 text-sm py-2">Nenhum foco encontrado para este tema.</p>
                              ) : (
                                filteredFocuses.map((focus) => (
                                  <Button
                                    key={focus.id}
                                    variant={selectedFocus === focus.name ? "secondary" : "ghost"}
                                    className="w-full justify-start mb-1"
                                    onClick={() => {
                                      console.log("🔍 Foco selecionado:", focus.name);
                                      setSelectedFocus(focus.name);
                                    }}
                                  >
                                    {selectedFocus === focus.name && (
                                      <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                                    )}
                                    <span className="truncate text-left">{focus.name}</span>
                                    <Badge
                                      className={`ml-2 ${getBadgeStyle(
                                        focus.name,
                                        "focus"
                                      )}`}
                                    >
                                      {getStatusEmoji(focus.name, "focus")}
                                      {getStatsText(focus.name, "focus")}
                                    </Badge>
                                  </Button>
                                ))
                              )}
                            </div>
                          </ScrollArea>
                        )}
                      </div>
                    )}

                    <div className="border rounded-lg p-3 bg-slate-50">
                      <div className="flex items-center gap-1 mb-1">
                        <Info className="h-4 w-4 text-blue-500" />
                        <span className="text-sm font-medium text-slate-700">Legenda:</span>
                      </div>
                      <div className="text-xs text-slate-600 space-y-1">
                        <div className="flex items-center gap-1">
                          <span>✅</span>
                          <span>Tópico já estudado (questões corretas/total)</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span>🆕</span>
                          <span>Tópico ainda não estudado</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="time" className="p-6 space-y-6">
              <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-2 flex items-start gap-3">
                <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-blue-700 break-words">
                  Configure o horário e a duração do seu estudo. Recomendamos blocos de 30 minutos a 2 horas para melhor aproveitamento.
                </p>
              </div>

              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="startTime" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">1</span>
                    Horário de Início
                  </Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="max-w-xs border-2 focus:border-green-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">2</span>
                    Duração do Estudo
                  </Label>
                  
                  {!showCustomDuration ? (
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {["0:30", "1:00", "1:30", "2:00"].map((value) => (
                          <Button 
                            key={value} 
                            type="button"
                            variant={duration === value ? "secondary" : "outline"}
                            size="sm"
                            onClick={() => handleDurationSelect(value)}
                            className="px-3"
                          >
                            {value}
                          </Button>
                        ))}
                        <Button
                          type="button"
                          variant={showCustomDuration ? "secondary" : "outline"}
                          size="sm"
                          onClick={() => setShowCustomDuration(true)}
                          className="px-3"
                        >
                          Personalizar
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Formato H:MM (ex: 1:30 significa 1 hora e 30 minutos)
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Input 
                          id="customDuration"
                          value={customDuration}
                          onChange={handleCustomDurationChange}
                          placeholder="1:30"
                          className="max-w-[100px] border-2 focus:border-green-500"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setShowCustomDuration(false)}
                          className="px-3"
                        >
                          Voltar às opções
                        </Button>
                      </div>
                      <p className={`text-xs ${isValidTimeFormat(customDuration) || !customDuration ? 'text-muted-foreground' : 'text-destructive'}`}>
                        {isValidTimeFormat(customDuration) || !customDuration
                          ? 'Use o formato H:MM (ex: 1:30 para 1h e 30min)'
                          : 'Formato inválido! Use H:MM (ex: 1:30)'}
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="activity" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">3</span>
                    Descrição da Atividade
                  </Label>
                  <Textarea
                    id="activity"
                    value={activity}
                    onChange={(e) => setActivity(e.target.value)}
                    placeholder="Ex: Leitura + Questões"
                    className="h-20 border-2 focus:border-green-500"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="help" className="p-6 space-y-6">
              <div className="bg-green-50 border border-green-100 rounded-lg p-4 mb-6 flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-green-700 break-words">
                  Revise os detalhes do seu tópico de estudo antes de confirmar.
                </p>
              </div>

              <div className="space-y-6">
                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Tópico de Estudo</h3>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Especialidade:</span>
                      <span>{isManual ? manualSpecialty : selectedSpecialty}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Tema:</span>
                      <span>{isManual ? manualTheme : selectedTheme}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Foco:</span>
                      <span>{isManual ? manualFocus : selectedFocus}</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Horário</h3>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Início:</span>
                      <span>{startTime}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Duração:</span>
                      <span>{showCustomDuration && customDuration ? customDuration : duration}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Atividade:</span>
                      <span>{activity || "Não especificada"}</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Dicas</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Revise seu estudo regularmente para melhor retenção</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Combine teoria e prática para fixar o conhecimento</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Faça pausas entre os períodos de estudo</span>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="border-t p-4 flex justify-between">
          <Button
            variant="outline"
            onClick={step > 1 ? prevStep : () => onOpenChange(false)}
          >
            {step > 1 ? "Voltar" : "Cancelar"}
          </Button>
          
          <Button 
            onClick={step < 3 ? nextStep : handleSave}
            disabled={!canContinue()}
            variant="duolingo"
            className="px-8"
          >
            {step < 3 ? "Continuar" : "Confirmar"}
          </Button>
        </div>

        {!isCreating && topic.id && (
          <DeleteTopicDialog
            open={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            topicId={topic.id}
            onDelete={handleDeleteComplete}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TopicEditDialog;
