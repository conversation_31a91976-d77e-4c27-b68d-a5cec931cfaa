
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { ThemeToggle } from "@/components/theme/ThemeToggle";
import { useTheme } from "@/context/ThemeContext";

export const HeaderLogo = () => {
  // This ensures that we're checking if the theme context is available
  const themeContext = useTheme();

  const { data: settings } = useQuery({
    queryKey: ["site-settings", "v2"], // Added version to force cache invalidation
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_site_settings")
        .select("*");

      if (error) throw error;

      return data.reduce((acc: Record<string, string>, curr) => {
        acc[curr.key] = curr.value;
        return acc;
      }, {});
    },
  });

  return (
    <div className="flex items-center gap-2">
      <Link to="/" className="flex items-center group shrink-0">
        <img
          src={settings?.logo_url || "/faviconx.webp"}
          alt="PedBook Logo"
          width={48}
          height={48}
          className="h-10 w-auto transition-transform hover:scale-105"
          fetchpriority="high"
        />
      </Link>
      {themeContext && (
        <ThemeToggle className="ml-1" size="sm" />
      )}
    </div>
  );
};
