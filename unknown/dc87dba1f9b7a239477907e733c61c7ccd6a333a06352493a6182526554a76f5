
export interface ImportQuestion {
  statement_text?: string;
  statement?: string;
  alternatives: string[];
  correct_answer: string | number;
  specialty: string;
  theme: string;
  focus: string;
  location?: string;
  year: number;
  institution_id?: string;
  topics?: string[];
  answer_type?: 'MULTIPLE_CHOICE' | 'DISCURSIVE' | 'TRUE_OR_FALSE';
  domain?: string;
  images?: string[] | string; // Array ou string única de URLs de imagens
  tipo?: string; // Tipo da questão (ex: teorica-1)
  numero?: number; // Número da questão
  is_annulled?: boolean; // Indica se a questão foi anulada
}

export interface ImportResults {
  success: number;
  errors: string[];
  created: {
    specialties: Map<string, CategoryResult>;
    themes: Map<string, CategoryResult>;
    focuses: Map<string, CategoryResult>;
    locations: Map<string, LocationResult>;
    years: Set<number>;
  };
}

export interface CategoryResult {
  id: string;
  name: string;
  type: string;
}

export interface LocationResult {
  id: string;
  name: string;
}
