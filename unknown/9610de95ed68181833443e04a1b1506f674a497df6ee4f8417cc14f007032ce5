import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON>, CheckCircle2, AlertCircle, Filter } from "lucide-react";
import { VaccineDose } from "@/components/admin/vaccine/types";
import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface VaccineAnalysisProps {
  ageInMonths: number;
}

interface GroupedDoses {
  [key: string]: VaccineDose[];
}

export function VaccineAnalysis({ ageInMonths }: VaccineAnalysisProps) {
  const [showOnlyCurrent, setShowOnlyCurrent] = useState(false);
  const [showOnlyUpcoming, setShowOnlyUpcoming] = useState(false);
  const [showSUS, setShowSUS] = useState(true);
  const [showPrivate, setShowPrivate] = useState(true);

  const { data: doses, error } = useQuery({
    queryKey: ['vaccine-doses'],
    queryFn: async () => {

      
      const { data, error } = await supabase
        .from('pedbook_vaccine_doses')
        .select(`
          id,
          dose_number,
          age_recommendation,
          type,
          dose_type,
          vaccine:pedbook_vaccines (
            id,
            name,
            description
          )
        `)
        .order('age_recommendation');

      if (error) {
        console.error("Error fetching vaccine doses:", error);
        throw error;
      }

      return data as VaccineDose[];
    },
  });

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erro</AlertTitle>
        <AlertDescription>
          Não foi possível carregar as informações das vacinas.
        </AlertDescription>
      </Alert>
    );
  }

  if (!doses) {
    return null;
  }

  const groupDosesByAge = (doses: VaccineDose[]): GroupedDoses => {
    return doses.reduce((groups: GroupedDoses, dose) => {
      const ageKey = dose.age_recommendation === "0" ? "Ao nascer" : `${dose.age_recommendation} meses`;
      if (!groups[ageKey]) {
        groups[ageKey] = [];
      }
      groups[ageKey].push(dose);
      return groups;
    }, {});
  };

  const findNextAgeGroup = (groupedDoses: GroupedDoses): string | null => {
    const ages = Object.keys(groupedDoses)
      .map(age => age === "Ao nascer" ? 0 : parseInt(age))
      .filter(age => age > ageInMonths)
      .sort((a, b) => a - b);
    
    if (ages.length === 0) return null;
    return ages[0] === 0 ? "Ao nascer" : `${ages[0]} meses`;
  };

  const filterDosesByType = (doses: VaccineDose[]) => {
    return doses.filter(dose => {
      const isSUS = dose.type === 'SUS';
      return (showSUS && isSUS) || (showPrivate && !isSUS);
    });
  };

  const groupedDoses = groupDosesByAge(doses);
  const nextAgeGroup = findNextAgeGroup(groupedDoses);

  const currentGroups: GroupedDoses = {};
  const upcomingGroups: GroupedDoses = {};

  Object.entries(groupedDoses).forEach(([ageKey, doseGroup]) => {
    const age = ageKey === "Ao nascer" ? 0 : parseInt(ageKey);
    if (age <= ageInMonths) {
      currentGroups[ageKey] = filterDosesByType(doseGroup);
    } else if (nextAgeGroup && ageKey === nextAgeGroup) {
      upcomingGroups[ageKey] = filterDosesByType(doseGroup);
    }
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Alert className="bg-yellow-50 border-yellow-200 flex-1 mr-4">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertTitle className="text-yellow-800">Vacinas Recomendadas</AlertTitle>
          <AlertDescription className="text-yellow-700">
            Com base na idade do paciente ({ageInMonths} meses)
          </AlertDescription>
        </Alert>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Filtros
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 p-4">
            <DropdownMenuLabel>Visualização</DropdownMenuLabel>
            <div className="flex items-center space-x-2 mt-2">
              <Checkbox 
                id="current" 
                checked={showOnlyCurrent} 
                onCheckedChange={(checked) => {
                  setShowOnlyCurrent(checked === true);
                  if (checked) setShowOnlyUpcoming(false);
                }}
              />
              <label htmlFor="current" className="text-sm">Apenas vacinas atuais</label>
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Checkbox 
                id="upcoming" 
                checked={showOnlyUpcoming}
                onCheckedChange={(checked) => {
                  setShowOnlyUpcoming(checked === true);
                  if (checked) setShowOnlyCurrent(false);
                }}
              />
              <label htmlFor="upcoming" className="text-sm">Apenas próximas vacinas</label>
            </div>
            <DropdownMenuSeparator className="my-2" />
            <DropdownMenuLabel>Tipo de Vacina</DropdownMenuLabel>
            <div className="flex items-center space-x-2 mt-2">
              <Checkbox 
                id="sus" 
                checked={showSUS}
                onCheckedChange={(checked) => setShowSUS(checked === true)}
              />
              <label htmlFor="sus" className="text-sm">SUS</label>
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Checkbox 
                id="private" 
                checked={showPrivate}
                onCheckedChange={(checked) => setShowPrivate(checked === true)}
              />
              <label htmlFor="private" className="text-sm">Particular</label>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {!showOnlyUpcoming && (
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2 text-green-700">
              <CheckCircle2 className="h-5 w-5" />
              Vacinas que já deveriam ter sido aplicadas
            </h3>
            <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
              {Object.entries(currentGroups).map(([ageKey, doseGroup]) => (
                <div key={ageKey} className="space-y-2">
                  <h4 className="font-medium text-green-800 border-b border-green-200 pb-1">
                    {ageKey}
                  </h4>
                  {doseGroup.map((dose) => (
                    <div key={dose.id} className="p-2 bg-green-50 rounded">
                      <div className="font-medium text-green-800">
                        💉 {dose.vaccine.name}
                        {dose.dose_type === 'reforço' ? ' (Reforço)' : ` (${dose.dose_number}ª dose)`}
                      </div>
                      <div className="text-sm text-green-600">
                        {dose.type}
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </Card>
        )}

        {!showOnlyCurrent && (
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2 text-blue-700">
              <CalendarClock className="h-5 w-5" />
              Próximas vacinas
            </h3>
            <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
              {Object.entries(upcomingGroups).map(([ageKey, doseGroup]) => (
                <div key={ageKey} className="space-y-2">
                  <h4 className="font-medium text-blue-800 border-b border-blue-200 pb-1">
                    {ageKey}
                  </h4>
                  {doseGroup.map((dose) => (
                    <div key={dose.id} className="p-2 bg-blue-50 rounded">
                      <div className="font-medium text-blue-800">
                        💉 {dose.vaccine.name}
                        {dose.dose_type === 'reforço' ? ' (Reforço)' : ` (${dose.dose_number}ª dose)`}
                      </div>
                      <div className="text-sm text-blue-600">
                        {dose.type}
                      </div>
                    </div>
                  ))}
                </div>
              ))}
              {Object.keys(upcomingGroups).length === 0 && (
                <div className="text-gray-500 text-center py-4">
                  Não há próximas vacinas previstas
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}