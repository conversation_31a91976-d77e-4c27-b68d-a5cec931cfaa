import { Button } from "@/components/ui/button";
import { useState } from "react";
import { BasicInfoInputs } from "./form/BasicInfoInputs";
import { MeasurementsInputs } from "./form/MeasurementsInputs";
import { AdditionalInfoCard } from "./form/AdditionalInfoCard";

interface PatientFormProps {
  onSubmit: (data: {
    age: number;
    weight: number;
    birthWeight: number;
    height: number;
    gender: "male" | "female";
    headCircumference: number;
    exclusiveBreastfeeding: boolean;
    hasRiskFactors: boolean;
    maturity: "Term" | "Pre-term";
  }) => void;
}

export function PatientForm({ onSubmit }: PatientFormProps) {
  const [exclusiveBreastfeeding, setExclusiveBreastfeeding] = useState(false);
  const [hasRiskFactors, setHasRiskFactors] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    onSubmit({
      age: Number(formData.get("age")),
      weight: Number(formData.get("weight")),
      birthWeight: Number(formData.get("birthWeight")),
      height: Number(formData.get("height")),
      gender: formData.get("gender") as "male" | "female",
      headCircumference: Number(formData.get("headCircumference")),
      exclusiveBreastfeeding,
      hasRiskFactors,
      maturity: formData.get("maturity") as "Term" | "Pre-term",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <BasicInfoInputs />
          <MeasurementsInputs />
        </div>

        <AdditionalInfoCard
          exclusiveBreastfeeding={exclusiveBreastfeeding}
          hasRiskFactors={hasRiskFactors}
          onBreastfeedingChange={setExclusiveBreastfeeding}
          onRiskFactorsChange={setHasRiskFactors}
        />
      </div>

      <Button type="submit" className="w-full">
        Analisar Paciente
      </Button>
    </form>
  );
}