
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Formula, Category, FAQItem } from "./types";
import { FAQ } from "./FAQ";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FormulaCategories } from "./components/FormulaCategories";
import { ChildcareSEO } from "@/components/seo/ChildcareSEO";
import { CHILDCARE_SEO_DATA } from "@/data/childcareSEOData";
import { supabase } from "@/integrations/supabase/client";

const Formulas = () => {
  const seoData = CHILDCARE_SEO_DATA['formulas'];

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedFormula, setSelectedFormula] = useState<Formula | null>(null);

  const { data: categories } = useQuery({
    queryKey: ["formula-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_formula_categories")
        .select("*")
        .order("name");
      if (error) throw error;
      return data as Category[];
    },
  });

  const { data: formulas } = useQuery({
    queryKey: ["formulas", selectedCategory],
    queryFn: async () => {
      let query = supabase.from("pedbook_formulas").select("*");
      if (selectedCategory) {
        query = query.eq("category_id", selectedCategory);
      }
      const { data, error } = await query;
      if (error) throw error;
      return data as Formula[];
    },
  });

  const faqItems: FAQItem[] = [
    {
      question: "O que são fórmulas infantis?",
      answer: "Fórmulas infantis são substitutos do leite materno desenvolvidos para suprir as necessidades nutricionais dos bebês que não podem ser amamentados. Elas são feitas com base no leite de vaca ou de soja e são modificadas para se aproximar da composição do leite materno. Elas fornecem uma mistura balanceada de proteínas, gorduras, carboidratos, vitaminas e minerais. Embora não repliquem os benefícios imunológicos do leite materno, são essenciais para bebês com contraindicações ao aleitamento."
    },
    {
      question: "Quando é indicada a utilização de fórmulas infantis?",
      answer: "A fórmula infantil é indicada quando o aleitamento materno não é possível ou é insuficiente. As principais indicações incluem: Dificuldades no aleitamento por parte da mãe; Condições clínicas específicas do bebê, como alergia à proteína do leite de vaca (APLV) ou intolerância à lactose; Situações em que a amamentação exclusiva não é praticada. Bebês prematuros, com baixo peso ao nascer, ou portadores de condições metabólicas também podem necessitar de fórmulas especiais. O acompanhamento pediátrico é essencial para ajustar o tipo e a quantidade de fórmula de acordo com as necessidades individuais."
    },
    {
      question: "Quais são os principais tipos de fórmulas infantis?",
      answer: "Os principais tipos de fórmulas infantis são:\n\nFórmulas de Partida: Indicadas para lactentes desde o nascimento até os 6 meses de idade.\nFórmulas de Seguimento: Utilizadas a partir dos 6 meses até os 12 meses, podendo se estender até os 36 meses em alguns casos.\nFórmulas de Primeira Infância: Voltadas para crianças de 1 a 3 anos de idade.\nFórmulas Antirregurgitação (AR): Mais espessas, ajudam a reduzir o refluxo.\nFórmulas de Soja: Indicadas para bebês com alergia à proteína do leite de vaca (APLV).\nFórmulas Extensamente Hidrolisadas: Usadas em casos de alergia alimentar grave.\nFórmulas de Aminoácidos: Utilizadas em bebês com alergias alimentares severas.\n\nExistem fórmulas específicas para bebês prematuros ou com restrições alimentares severas, que possuem uma composição de nutrientes mais balanceada, facilitando o crescimento e desenvolvimento."
    },
    {
      question: "Como preparar corretamente uma fórmula infantil?",
      answer: "Para preparar a fórmula corretamente:\nÁgua: Utilize água filtrada ou fervida. Caso opte por ferver a água, deixe-a esfriar até atingir aproximadamente 70°C antes de misturá-la com o pó da fórmula.\nProporção: Siga a proporção recomendada pelo fabricante (geralmente 1 medida de pó para cada 30 mL de água).\nHigiene: Lave bem as mãos e esterilize todos os utensílios, como mamadeiras, bicos e tampas.\nMistura: Adicione a quantidade correta de pó da fórmula à água preparada e agite até dissolver completamente.\nTeste de temperatura: Teste a temperatura da fórmula no pulso antes de oferecer ao bebê. Deve estar morna, nunca quente.\n\nAs fórmulas preparadas devem ser consumidas em até 2 horas. Caso contrário, descarte a fórmula. Evite deixá-la exposta à temperatura ambiente para prevenir a proliferação de bactérias."
    },
    {
      question: "Quais são os volumes recomendados para oferecer fórmulas de acordo com a idade do bebê?",
      answer: "Nascimento até 30 dias: 60-120 mL por administração, 6-8 vezes ao dia.\n1 a 2 meses: 120-150 mL por administração, 6-8 vezes ao dia.\n2 a 4 meses: 150-180 mL por administração, 5-6 vezes ao dia.\n4 a 8 meses: 180-200 mL por administração, 5-6 vezes ao dia.\nAcima de 8 meses: 200-250 mL por administração, 4-5 vezes ao dia.\n\nEsses valores podem variar conforme o desenvolvimento e o ganho de peso da criança. O pediatra deve ajustar os volumes de acordo com o estado clínico e as necessidades nutricionais do bebê."
    },
    {
      question: "Quando posso oferecer suco de frutas ao meu bebê?",
      answer: "A Sociedade Brasileira de Pediatria recomenda que sucos de frutas não sejam oferecidos a bebês com menos de 1 ano de idade, pois eles não trazem benefícios nutricionais significativos nessa fase. A introdução de alimentos sólidos deve seguir o aleitamento materno e a recomendação de alimentos naturais. A introdução de frutas deve ser feita de forma gradual e em pedaços, a partir dos 6 meses, para que o bebê desenvolva suas habilidades mastigatórias."
    },
    {
      question: "Qual a diferença entre fórmulas infantis e compostos lácteos?",
      answer: "Fórmulas infantis são desenvolvidas para atender as necessidades nutricionais dos lactentes (bebês menores de 12 meses), enquanto compostos lácteos são direcionados para crianças acima de 1 ano de idade. Os compostos lácteos, muitas vezes chamados de 'leites de crescimento', têm uma composição menos rigorosa e podem conter ingredientes adicionais como açúcares e aditivos. Eles não são indicados para bebês menores de 1 ano. O consumo antes dos 12 meses pode levar a deficiências nutricionais e sobrecarga renal no bebê."
    },
    {
      question: "O leite de vaca pode substituir a fórmula infantil?",
      answer: "Não. O leite de vaca integral não é adequado para crianças menores de 1 ano devido ao seu alto teor de proteínas, sódio, ácidos graxos saturados e menor biodisponibilidade de nutrientes essenciais como ferro e zinco. A fórmula infantil é a melhor alternativa nutricional caso o leite materno não esteja disponível."
    },
    {
      question: "Quais são os possíveis riscos do uso prolongado de mamadeiras?",
      answer: "O uso prolongado de mamadeiras, especialmente após os 4 meses de idade, pode causar problemas no desenvolvimento da cavidade bucal e no sistema respiratório. Recomenda-se a transição para o uso de copinhos para evitar essas complicações. O uso prolongado de mamadeiras também está associado ao aumento do risco de cáries dentárias e infecções do ouvido médio. A transição para copos de transição entre 6-9 meses de vida deve ser incentivada."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-purple-900/20">
      <ChildcareSEO {...seoData} />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-12">
        <Link 
          to="/puericultura" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative mb-16"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-pink-900/20 rounded-3xl blur-xl"></div>
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ type: "spring", duration: 1, bounce: 0.3 }}
            className="relative glass-effect rounded-3xl p-10 backdrop-blur-sm border border-white/20 dark:border-white/10 overflow-hidden bg-white/50 dark:bg-slate-800/50"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 animate-pulse dark:from-primary/10 dark:to-purple-500/10"></div>
            <div className="relative z-10">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary via-purple-600 to-pink-500 bg-clip-text text-transparent text-center mb-4">
                Fórmulas Infantis
              </h1>
              <p className="text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-lg text-center leading-relaxed">
                Explore nossa seleção cuidadosamente organizada de fórmulas alimentares, 
                classificadas por categorias e faixas etárias para o melhor desenvolvimento do seu bebê.
              </p>
            </div>
          </motion.div>
        </motion.div>

        <FormulaCategories
          categories={categories}
          formulas={formulas}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          selectedFormula={selectedFormula}
          setSelectedFormula={setSelectedFormula}
        />

        <div className="max-w-3xl mx-auto mt-16">
          <FAQ items={faqItems} />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Formulas;
