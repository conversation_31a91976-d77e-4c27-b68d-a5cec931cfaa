import React, { useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Newspaper, ArrowRight, ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useNewsletters } from '@/hooks/useNewsletters';
import { NewsItem } from '@/types/newsletter';

const NewsItemPreview: React.FC<{ news: NewsItem }> = ({ news }) => {
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return format(date, "d 'de' MMMM", { locale: ptBR });
    } catch (error) {
      return '';
    }
  };

  return (
    <div
      className="flex-shrink-0 w-36 sm:w-52 group cursor-pointer hover:bg-white/40 dark:hover:bg-slate-800/40 transition-colors rounded overflow-hidden border border-gray-100/40 dark:border-gray-700/10"
      onClick={() => news.link && window.open(news.link, '_blank')}
    >
      <div className="relative h-16 overflow-hidden">
        {news.image_url ? (
          <img
            src={news.image_url}
            alt={news.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
              (e.target as HTMLImageElement).parentElement!.classList.add('bg-blue-50', 'dark:bg-blue-900/20', 'flex', 'items-center', 'justify-center');
              const icon = document.createElement('div');
              icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-blue-500/60 dark:text-blue-400/60"><path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2z"></path><path d="M2 18V6a2 2 0 0 1 2-2h12"></path></svg>';
              (e.target as HTMLImageElement).parentElement!.appendChild(icon);
            }}
          />
        ) : (
          <div className="w-full h-full bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
            <Newspaper className="h-5 w-5 text-blue-500/60 dark:text-blue-400/60" />
          </div>
        )}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-1">
          <div className="flex items-center text-[8px] text-white/90">
            {news.source && <span className="truncate font-medium">{news.source}</span>}
            {news.source && news.pub_date && <span className="mx-0.5">•</span>}
            {news.pub_date && <span>{formatDate(news.pub_date)}</span>}
          </div>
        </div>
      </div>
      <div className="p-1">
        <h3 className="font-medium text-gray-700 dark:text-gray-300 text-[9px] line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {news.title}
        </h3>
      </div>
    </div>
  );
};

const LatestNewsCard: React.FC = () => {
  const navigate = useNavigate();
  const { data: news, isLoading, isError } = useNewsletters({ limit: 5 });
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = React.useState(false);
  const [showRightArrow, setShowRightArrow] = React.useState(true);
  const [isDragging, setIsDragging] = React.useState(false);
  const [startX, setStartX] = React.useState(0);
  const [scrollLeft, setScrollLeft] = React.useState(0);

  const handleViewAll = () => {
    navigate('/newsletters');
  };

  // Controle de rolagem horizontal
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (container.scrollLeft > 10) {
        setShowLeftArrow(true);
      } else {
        setShowLeftArrow(false);
      }

      if (container.scrollLeft + container.clientWidth >= container.scrollWidth - 10) {
        setShowRightArrow(false);
      } else {
        setShowRightArrow(true);
      }
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll(); // Verificar estado inicial

    return () => container.removeEventListener('scroll', handleScroll);
  }, [news]);

  // Efeito para adicionar um manipulador de evento wheel para impedir a navegação de página
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Função para impedir a navegação de página quando o usuário está rolando horizontalmente
    const handleWheel = (e: WheelEvent) => {
      // Se o evento de roda tiver componente horizontal significativo
      if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        // Verificar se o container pode rolar mais nessa direção
        const canScrollLeft = container.scrollLeft > 0 && e.deltaX < 0;
        const canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth && e.deltaX > 0;

        if (canScrollLeft || canScrollRight) {
          e.preventDefault();
        }
      }
    };

    // Adicionar o manipulador com a opção passive: false para permitir preventDefault
    container.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, []);

  // Funções para controle de arrasto
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current!.offsetLeft);
    setScrollLeft(scrollContainerRef.current!.scrollLeft);
    document.body.style.overflowX = 'hidden'; // Prevenir rolagem da página
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    // Prevenir o comportamento padrão para evitar navegação de página
    e.stopPropagation();

    setIsDragging(true);
    setStartX(e.touches[0].pageX - scrollContainerRef.current!.offsetLeft);
    setScrollLeft(scrollContainerRef.current!.scrollLeft);
    document.body.style.overflowX = 'hidden'; // Prevenir rolagem da página
  };

  const handleMouseUp = (e?: React.MouseEvent | React.TouchEvent) => {
    if (e && isDragging) {
      // Se estávamos arrastando e é um evento de toque, prevenir a propagação
      e.stopPropagation();
    }

    setIsDragging(false);
    document.body.style.overflowX = ''; // Restaurar rolagem da página
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current!.offsetLeft;
    const walk = (x - startX) * 1.5; // Velocidade de rolagem
    scrollContainerRef.current!.scrollLeft = scrollLeft - walk;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;

    // Apenas interromper a propagação, não podemos usar preventDefault em eventos passivos
    e.stopPropagation();

    const x = e.touches[0].pageX - scrollContainerRef.current!.offsetLeft;
    const walk = (x - startX) * 1.5; // Velocidade de rolagem
    scrollContainerRef.current!.scrollLeft = scrollLeft - walk;
  };

  const scrollLeft10px = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -100, behavior: 'smooth' });
    }
  };

  const scrollRight10px = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 100, behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Título "Notícias Recentes" com emoji - posicionado acima do carrossel */}
      <div className="flex items-center mb-1 px-1">
        <div className="flex items-center opacity-80 hover:opacity-100 transition-opacity">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-newspaper h-3 w-3 mr-1 text-blue-500 dark:text-blue-400">
            <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
            <path d="M18 14h-8"></path>
            <path d="M15 18h-5"></path>
            <path d="M10 6h8v4h-8V6Z"></path>
          </svg>
          <span className="text-[9px] font-medium text-gray-600 dark:text-gray-400">
            Notícias Recentes
          </span>
        </div>
      </div>

      <div className="relative overscroll-none">
        {/* Seta para esquerda */}
        {showLeftArrow && (
          <button
            onClick={scrollLeft10px}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-6 h-6 flex items-center justify-center bg-white/80 dark:bg-slate-800/80 rounded-full shadow-sm border border-gray-100 dark:border-gray-700/30 cursor-pointer"
            aria-label="Rolar para esquerda"
          >
            <ChevronLeft className="h-3 w-3 text-gray-600 dark:text-gray-300" />
          </button>
        )}

        {/* Seta para direita */}
        {showRightArrow && (
          <button
            onClick={scrollRight10px}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-6 h-6 flex items-center justify-center bg-white/80 dark:bg-slate-800/80 rounded-full shadow-sm border border-gray-100 dark:border-gray-700/30 cursor-pointer"
            aria-label="Rolar para direita"
          >
            <ChevronRight className="h-3 w-3 text-gray-600 dark:text-gray-300" />
          </button>
        )}

        <div
          ref={scrollContainerRef}
          className="overflow-x-auto hide-scrollbar touch-pan-x"
          style={{ touchAction: 'pan-x' }}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={(e) => handleMouseUp(e)}
          onMouseMove={handleMouseMove}
          onTouchStart={handleTouchStart}
          onTouchEnd={(e) => {
            // Prevenir a propagação para evitar navegação de página
            e.stopPropagation();
            handleMouseUp(e);
          }}
          onTouchMove={handleTouchMove}
          onTouchCancel={(e) => {
            // Também tratar o evento de cancelamento de toque
            e.stopPropagation();
            handleMouseUp(e);
          }}
        >
          <div className="flex gap-1.5 px-1 py-0.5">
            {isLoading ? (
              // Esqueletos de carregamento
              [...Array(3)].map((_, i) => (
                <div key={i} className="flex-shrink-0 w-36 sm:w-52 rounded overflow-hidden border border-gray-100/40 dark:border-gray-700/10">
                  <Skeleton className="w-full h-16" />
                  <div className="p-1 space-y-0.5">
                    <Skeleton className="h-1.5 w-full" />
                    <Skeleton className="h-1.5 w-3/4" />
                  </div>
                </div>
              ))
            ) : isError ? null : news && news.length > 0 ? (
              <>
                {news.map((item) => (
                  <NewsItemPreview key={item.id} news={item} />
                ))}
                <div
                  className="flex-shrink-0 w-10 flex items-center justify-center cursor-pointer hover:bg-gray-50/70 dark:hover:bg-slate-800/30 rounded border border-gray-100/40 dark:border-gray-700/10"
                  onClick={handleViewAll}
                >
                  <div className="flex flex-col items-center gap-1 p-1">
                    <Newspaper className="h-3 w-3 text-blue-500/60 dark:text-blue-400/60" />
                    <ArrowRight className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                  </div>
                </div>
              </>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LatestNewsCard;
