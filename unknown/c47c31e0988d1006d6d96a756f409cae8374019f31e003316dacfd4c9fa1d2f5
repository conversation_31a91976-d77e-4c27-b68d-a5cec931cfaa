import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface UseGrowthDataProps {
  gender: 'male' | 'female';
  enabled: boolean;
}

export function useGrowthData({ gender, enabled }: UseGrowthDataProps) {
  const { data: weightData } = useQuery({
    queryKey: ['growth-curve-metadata', gender, 'weight'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('*')
        .eq('type', 'weight')
        .eq('gender', gender);

      if (error) throw error;
      return data?.[0] ?? null;
    },
    enabled: !!gender && enabled,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 48 * 60 * 60 * 1000, // 48 hours
  });

  const { data: heightData } = useQuery({
    queryKey: ['growth-curve-metadata', gender, 'height'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('*')
        .eq('type', 'height')
        .eq('gender', gender);

      if (error) throw error;
      return data?.[0] ?? null;
    },
    enabled: !!gender && enabled,
    staleTime: 24 * 60 * 60 * 1000,
    gcTime: 48 * 60 * 60 * 1000,
  });

  const { data: bmiData } = useQuery({
    queryKey: ['growth-curve-metadata', gender, 'bmi'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('*')
        .eq('type', 'bmi')
        .eq('gender', gender);

      if (error) throw error;
      return data?.[0] ?? null;
    },
    enabled: !!gender && enabled,
    staleTime: 24 * 60 * 60 * 1000,
    gcTime: 48 * 60 * 60 * 1000,
  });

  const { data: headCircumferenceData } = useQuery({
    queryKey: ['growth-curve-metadata', gender, 'head-circumference'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('*')
        .eq('type', 'head-circumference')
        .eq('gender', gender);

      if (error) throw error;
      return data?.[0] ?? null;
    },
    enabled: !!gender && enabled,
    staleTime: 24 * 60 * 60 * 1000,
    gcTime: 48 * 60 * 60 * 1000,
  });

  return {
    weightData,
    heightData,
    bmiData,
    headCircumferenceData
  };
}