import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Brain, Milestone, ArrowRight } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface DNPMAnalysisProps {
  ageInMonths: number;
}

interface Milestone {
  id: string;
  age_type: string;
  age_years: number | null;
  age_months: number;
  social_emotional: string | null;
  language_communication: string | null;
  cognition: string | null;
  motor_physical: string | null;
  image_url: string | null;
}

export function DNPMAnalysis({ ageInMonths }: DNPMAnalysisProps) {
  const { data: milestones, isLoading } = useQuery({
    queryKey: ["dnpm-milestones"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_dnpm_milestones")
        .select("*")
        .order("age_months");

      if (error) throw error;
      return data as Milestone[];
    },
  });

  if (isLoading || !milestones) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }

  const currentMilestone = milestones
    .filter(m => m.age_months <= ageInMonths)
    .slice(-1)[0];

  const nextMilestone = milestones
    .find(m => m.age_months > ageInMonths);

  if (!currentMilestone) {
    return (
      <Alert>
        <Brain className="h-4 w-4" />
        <AlertTitle>DNPM</AlertTitle>
        <AlertDescription>
          Não há marcos de desenvolvimento disponíveis para esta idade.
        </AlertDescription>
      </Alert>
    );
  }

  const MilestoneCard = ({ title, content, bgColor, textColor }: { title: string, content: string | null, bgColor: string, textColor: string }) => {
    if (!content) return null;
    return (
      <div className={`${bgColor} rounded-lg p-4 shadow-sm`}>
        <h4 className={`text-sm font-medium ${textColor} mb-2 flex items-center gap-2`}>
          <span className="w-2 h-2 rounded-full bg-current"></span>
          {title}
        </h4>
        <p className={`text-sm ${textColor} break-words whitespace-pre-wrap`}>{content}</p>
      </div>
    );
  };

  const renderMilestoneSection = (milestone: Milestone, isNext: boolean = false) => {
    const baseColors = isNext ? {
      social: "bg-blue-50 text-blue-700",
      language: "bg-purple-50 text-purple-700",
      cognition: "bg-green-50 text-green-700",
      motor: "bg-orange-50 text-orange-700",
    } : {
      social: "bg-blue-100 text-blue-800",
      language: "bg-purple-100 text-purple-800",
      cognition: "bg-green-100 text-green-800",
      motor: "bg-orange-100 text-orange-800",
    };

    return (
      <div className="space-y-3">
        <MilestoneCard
          title="Social e Emocional"
          content={milestone.social_emotional}
          bgColor={baseColors.social.split(" ")[0]}
          textColor={baseColors.social.split(" ")[1]}
        />
        <MilestoneCard
          title="Linguagem e Comunicação"
          content={milestone.language_communication}
          bgColor={baseColors.language.split(" ")[0]}
          textColor={baseColors.language.split(" ")[1]}
        />
        <MilestoneCard
          title="Cognição"
          content={milestone.cognition}
          bgColor={baseColors.cognition.split(" ")[0]}
          textColor={baseColors.cognition.split(" ")[1]}
        />
        <MilestoneCard
          title="Motora/Física"
          content={milestone.motor_physical}
          bgColor={baseColors.motor.split(" ")[0]}
          textColor={baseColors.motor.split(" ")[1]}
        />
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Alert className="bg-yellow-50 border-yellow-200">
        <Brain className="h-4 w-4 text-yellow-600" />
        <AlertTitle className="text-yellow-800">Marcos do Desenvolvimento</AlertTitle>
        <AlertDescription className="text-yellow-700">
          Com base na idade do paciente ({ageInMonths} meses)
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 overflow-hidden">
          <div className="flex items-center gap-2 text-gray-700 mb-4">
            <Milestone className="h-5 w-5" />
            <h3 className="font-semibold">Marco Atual ({currentMilestone.age_months} meses)</h3>
          </div>
          {renderMilestoneSection(currentMilestone)}
        </Card>

        {nextMilestone && (
          <Card className="p-6 overflow-hidden">
            <div className="flex items-center gap-2 text-gray-700 mb-4">
              <ArrowRight className="h-5 w-5" />
              <h3 className="font-semibold">Próximo Marco ({nextMilestone.age_months} meses)</h3>
            </div>
            {renderMilestoneSection(nextMilestone, true)}
          </Card>
        )}
      </div>

      <p className="text-center text-sm text-gray-500 mt-6">
        Referência: Cartilha de Desenvolvimento 2m-5anos, CDC/SBP
      </p>
    </div>
  );
}