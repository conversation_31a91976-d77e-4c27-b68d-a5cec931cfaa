
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@400;500;600&family=Quicksand:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221 100% 50%;
    --primary-rgb: 0, 102, 255; /* RGB value for primary color */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 100% 50%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 9.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-rgb: 59, 130, 246; /* RGB value for primary color in dark mode */
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-white dark:bg-slate-900 text-foreground font-inter transition-colors duration-300;
    touch-action: manipulation;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-semibold;
  }
}

/* Custom heading classes for consistent styling */
.gradient-heading {
  @apply font-medium text-primary dark:text-blue-400;
}

.title-gradient {
  @apply font-semibold text-primary dark:text-blue-300;
}

/* Card styling improvements */
.card-container {
  @apply bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700 rounded-xl overflow-hidden;
}

.card-header {
  @apply flex items-center gap-3 mb-3;
}

.icon-container {
  @apply p-2.5 rounded-lg shadow-sm;
}

/* Fixed gradient text for dark mode */
.gradient-text {
  @apply bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent;
}

.dark .gradient-text {
  background-image: linear-gradient(to right, rgb(96 165 250), rgb(59 130 246));
  background-clip: text;
  color: transparent;
}

/* Card hover effect */
.card-hover {
  @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Cloud animations - Velocidade aumentada */
@keyframes moveRightToLeft {
  0% {
    transform: translateX(-200px);
  }
  100% {
    transform: translateX(calc(100vw + 200px));
  }
}

@keyframes moveLeftToRight {
  0% {
    transform: translateX(calc(100vw + 200px));
  }
  100% {
    transform: translateX(-200px);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.glass-card {
  @apply bg-white/55 dark:bg-slate-800/75 backdrop-blur-md shadow-lg  dark:border-slate-700/50;
}

/* Improved folder card styling for dark mode */
.folder-card {
  @apply bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700 border border-gray-100 dark:border-slate-700 shadow-sm hover:shadow-md transition-all;
}

.folder-card-text {
  @apply text-gray-900 dark:text-gray-100;
}

.folder-icon {
  @apply text-primary dark:text-blue-400;
}

.folder-actions {
  @apply text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors;
}

.hover-scale {
  @apply transition-transform hover:scale-105;
}

/* Dark mode adjustments for specific components */
.dark .text-primary {
  @apply text-blue-400;
}

/* Fix for contrast in dark mode */
.dark .text-muted-foreground {
  @apply text-gray-300;
}

.dark .border-primary\/10 {
  @apply border-primary/30;
}

.dark .bg-primary\/5 {
  @apply bg-primary/20;
}

.dark .bg-white {
  @apply bg-slate-800;
}

.dark .bg-white\/80 {
  @apply bg-slate-800/80;
}

.dark .bg-white\/50 {
  @apply bg-slate-800/50;
}

.dark .hover\:bg-white:hover {
  @apply hover:bg-slate-700;
}

/* Enhanced card category styles for dark mode */
.dark .card-amber { @apply bg-amber-800/50 border-amber-700/70 text-amber-200; }
.dark .card-emerald { @apply bg-emerald-800/50 border-emerald-700/70 text-emerald-200; }
.dark .card-purple { @apply bg-purple-800/50 border-purple-700/70 text-purple-200; }
.dark .card-blue { @apply bg-blue-800/50 border-blue-700/70 text-blue-200; }
.dark .card-red { @apply bg-red-800/50 border-red-700/70 text-red-200; }
.dark .card-cyan { @apply bg-cyan-800/50 border-cyan-700/70 text-cyan-200; }
.dark .card-green { @apply bg-green-800/50 border-green-700/70 text-green-200; }
.dark .card-yellow { @apply bg-yellow-800/50 border-yellow-700/70 text-yellow-200; }
.dark .card-indigo { @apply bg-indigo-800/50 border-indigo-700/70 text-indigo-200; }
.dark .card-rose { @apply bg-rose-800/50 border-rose-700/70 text-rose-200; }

/* Category cards dark mode improvements */
.dark .text-primary-foreground {
  @apply text-white;
}

/* Text container backgrounds - improve contrast in dark mode */
.dark .gradient-container {
  background: linear-gradient(to right, rgb(30 41 59), rgb(15 23 42));
}

.dark .gradient-container-br {
  background: linear-gradient(to bottom right, rgb(30 41 59), rgb(15 23 42));
}

/* Fix for text in dark mode */
.dark .text-gray-600,
.dark .text-gray-700,
.dark .text-gray-800 {
  @apply text-gray-300;
}

.dark .text-gray-500 {
  @apply text-gray-400;
}

.dark .text-gray-900 {
  @apply text-gray-100;
}

/* Glow effects for dark mode */
.dark .card-glow-hover:hover {
  @apply shadow-md shadow-primary/25;
}

.dark .bg-gray-50 {
  @apply bg-slate-800/80 text-gray-200;
}

/* Dialog and component styling for dark mode */
.dark .glass-card {
  @apply bg-slate-800/90 backdrop-blur-md shadow-lg border border-slate-700/60;
}

/* Medication List improvements */
.dark .medication-card {
  @apply bg-slate-800 border-slate-700 hover:bg-slate-700/80;
}

.dark .medication-name {
  @apply text-blue-400;
}

.dark .medication-category {
  @apply bg-slate-700/80 text-blue-300 border-slate-600;
}

/* Keep existing code */

@keyframes subtle-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
}

.animate-subtle-pulse {
  animation: subtle-pulse 3s ease-in-out infinite;
}

.perspective {
  perspective: 2000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.dialog-content-70 {
  max-width: 70% !important;
}

/* Enhanced card styling */
.card-enhanced {
  @apply shadow-md dark:shadow-slate-800/50 transition-all duration-300;
}

.card-enhanced:hover {
  @apply shadow-lg dark:shadow-slate-700/50 -translate-y-1;
}

/* Card border glow on hover */
.card-glow-hover:hover {
  @apply shadow-md shadow-primary/40 dark:shadow-primary/20;
}

/* Enhanced card category styles - Updated for stronger backgrounds and visible borders */
.card-amber { @apply bg-amber-200 dark:bg-amber-800/40 border-amber-500 border-2 dark:border-amber-700; }
.card-emerald { @apply bg-emerald-200 dark:bg-emerald-800/40 border-emerald-500 border-2 dark:border-emerald-700; }
.card-purple { @apply bg-purple-200 dark:bg-purple-800/40 border-purple-500 border-2 dark:border-purple-700; }
.card-blue { @apply bg-blue-200 dark:bg-blue-800/40 border-blue-500 border-2 dark:border-blue-700; }
.card-red { @apply bg-red-200 dark:bg-red-800/40 border-red-500 border-2 dark:border-red-700; }
.card-cyan { @apply bg-cyan-200 dark:bg-cyan-800/40 border-cyan-500 border-2 dark:border-cyan-700; }
.card-green { @apply bg-green-200 dark:bg-green-800/40 border-green-500 border-2 dark:border-green-700; }
.card-yellow { @apply bg-yellow-200 dark:bg-yellow-800/40 border-yellow-500 border-2 dark:border-yellow-700; }
.card-indigo { @apply bg-indigo-200 dark:bg-indigo-800/40 border-indigo-500 border-2 dark:border-indigo-700; }
.card-rose { @apply bg-rose-200 dark:bg-rose-800/40 border-rose-500 border-2 dark:border-rose-700; }

/* Mobile bottom spacing for content - Removido para evitar conflito com navegação móvel dinâmica */

/* Ghibli-inspired title styles - Redesigned for hand-drawn look */
.ghibli-title {
  font-family: 'Poppins', sans-serif;
  color: #4d8061; /* Verde Ghibli */
  position: relative;
  text-shadow:
    1px 1px 0 #f5f5dc,
    -1px 1px 0 #f5f5dc,
    1px -1px 0 #f5f5dc,
    -1px -1px 0 #f5f5dc;
  letter-spacing: 2px;
  font-weight: 700;
}

/* Dark mode adjustments for Ghibli title */
.dark .ghibli-title {
  color: #6fd08a;
  text-shadow:
    1px 1px 0 #1a2e22,
    -1px 1px 0 #1a2e22,
    1px -1px 0 #1a2e22,
    -1px -1px 0 #1a2e22;
}

/* Improve icon appearance in dark mode */
.dark .category-icon {
  @apply text-blue-400;
}

/* Enhanced icons colors for dark mode */
.dark .text-amber-700 { @apply text-amber-300; }
.dark .text-emerald-700 { @apply text-emerald-300; }
.dark .text-purple-700 { @apply text-purple-300; }
.dark .text-blue-700 { @apply text-blue-300; }
.dark .text-red-700 { @apply text-red-300; }
.dark .text-cyan-700 { @apply text-cyan-300; }
.dark .text-green-700 { @apply text-green-300; }
.dark .text-yellow-700 { @apply text-yellow-300; }
.dark .text-indigo-700 { @apply text-indigo-300; }
.dark .text-rose-700 { @apply text-rose-300; }
.dark .text-orange-700 { @apply text-orange-300; }
.dark .text-pink-700 { @apply text-pink-300; }
.dark .text-violet-700 { @apply text-violet-300; }
.dark .text-teal-700 { @apply text-teal-300; }
.dark .text-sky-700 { @apply text-sky-300; }
.dark .text-fuchsia-700 { @apply text-fuchsia-300; }
.dark .text-lime-700 { @apply text-lime-300; }

/* Enhanced backgrounds colors for dark mode */
.dark .bg-amber-50 { @apply bg-amber-900/30; }
.dark .bg-emerald-50 { @apply bg-emerald-900/30; }
.dark .bg-purple-50 { @apply bg-purple-900/30; }
.dark .bg-blue-50 { @apply bg-blue-900/30; }
.dark .bg-red-50 { @apply bg-red-900/30; }
.dark .bg-cyan-50 { @apply bg-cyan-900/30; }
.dark .bg-green-50 { @apply bg-green-900/30; }
.dark .bg-yellow-50 { @apply bg-yellow-900/30; }
.dark .bg-indigo-50 { @apply bg-indigo-900/30; }
.dark .bg-rose-50 { @apply bg-rose-900/30; }
.dark .bg-orange-50 { @apply bg-orange-900/30; }
.dark .bg-pink-50 { @apply bg-pink-900/30; }
.dark .bg-violet-50 { @apply bg-violet-900/30; }
.dark .bg-teal-50 { @apply bg-teal-900/30; }
.dark .bg-sky-50 { @apply bg-sky-900/30; }
.dark .bg-fuchsia-50 { @apply bg-fuchsia-900/30; }
.dark .bg-lime-50 { @apply bg-lime-900/30; }

/* Dark mode select improvements */
.dark select,
.dark [role="combobox"] {
  @apply bg-slate-800 border-slate-700 text-gray-200;
}

/* Esconder barra de rolagem para carrosséis */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Prevenir navegação de página ao arrastar horizontalmente */
.touch-pan-x {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-x: contain;
  overscroll-behavior: contain;
  -ms-scroll-chaining: none;
}

.overscroll-none {
  overscroll-behavior: none;
  -ms-scroll-chaining: none;
}

/* Correção específica para scroll horizontal no iPhone */
@media only screen and (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px) {
  html {
    overflow-x: hidden;
  }

  body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
  }
}

/* Classe para garantir que o texto seja selecionável */
.selectable-text {
  -webkit-user-select: text;
  user-select: text;
  -webkit-touch-callout: default;
}

/* Garantir que dialogs fiquem acima da navegação mobile */
[data-radix-dialog-overlay] {
  z-index: 10000 !important;
}

[data-radix-dialog-content] {
  z-index: 10001 !important;
}
