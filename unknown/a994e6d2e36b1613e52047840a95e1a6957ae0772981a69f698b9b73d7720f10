import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Baby } from "lucide-react";

interface BasicInfoInputsProps {
  defaultMaturity?: "Term" | "Pre-term";
}

export function BasicInfoInputs({ defaultMaturity = "Term" }: BasicInfoInputsProps) {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="age" className="flex items-center gap-2">
          <Baby className="h-4 w-4" />
          Idade (meses)
        </Label>
        <Input
          id="age"
          name="age"
          type="number"
          min={0}
          required
          className="bg-white"
        />
      </div>

      <div className="space-y-2">
        <Label className="flex items-center gap-2">G<PERSON>nero</Label>
        <RadioGroup name="gender" className="flex gap-4" defaultValue="male">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="male" id="male" />
            <Label htmlFor="male">Masculino</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="female" id="female" />
            <Label htmlFor="female">Feminino</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label htmlFor="maturity" className="flex items-center gap-2">
          <Baby className="h-4 w-4" />
          Maturidade
        </Label>
        <Select name="maturity" defaultValue={defaultMaturity}>
          <SelectTrigger className="bg-white">
            <SelectValue placeholder="Selecione a maturidade" />
          </SelectTrigger>
          <SelectContent className="bg-white">
            <SelectItem value="Term">A termo</SelectItem>
            <SelectItem value="Pre-term">Pré-termo</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </>
  );
}