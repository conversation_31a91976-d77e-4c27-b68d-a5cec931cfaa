
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface DenguePatientFormProps {
  weight: string;
  age: string;
  onWeightChange: (value: string) => void;
  onAgeChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export const DenguePatientForm = ({
  weight,
  age,
  onWeightChange,
  onAgeChange,
  onSubmit,
}: DenguePatientFormProps) => {
  console.log("🔄 Renderizando formulário Dengue com peso:", weight, "e idade:", age);
  
  return (
    <form onSubmit={onSubmit} className="space-y-6 max-w-md mx-auto animate-fade-in">
      <div className="space-y-2">
        <Label htmlFor="weight" className="text-gray-700 dark:text-gray-200">Peso do paciente (kg)</Label>
        <Input
          id="weight"
          type="number"
          step="0.1"
          min="0"
          required
          value={weight}
          onChange={(e) => onWeightChange(e.target.value)}
          className={getThemeClasses.input("bg-white/50 dark:bg-slate-800/50 border-orange-200 dark:border-orange-900/30")}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="age" className="text-gray-700 dark:text-gray-200">Idade do paciente (anos)</Label>
        <Input
          id="age"
          type="number"
          step="0.1"
          min="0"
          required
          value={age}
          onChange={(e) => onAgeChange(e.target.value)}
          className={getThemeClasses.input("bg-white/50 dark:bg-slate-800/50 border-orange-200 dark:border-orange-900/30")}
        />
      </div>

      <Button 
        type="submit" 
        className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700 text-white"
      >
        Iniciar Avaliação
      </Button>
    </form>
  );
};
