import { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { VaccineRelatedForm } from "./VaccineRelatedForm";
import { VaccineDialogProps } from "./types";
import { useVaccineForm } from "./useVaccineForm";
import { useAvailableVaccines, useExistingRelationships } from "./useVaccineQueries";

export function VaccineDialog({ open, onOpenChange, vaccine }: VaccineDialogProps) {
  const {
    name,
    setName,
    description,
    setDescription,
    selectedVaccines,
    setSelectedVaccines,
    handleSubmit,
    handleVaccineSelection,
    handleDoseNumberChange,
    handleDoseTypeChange
  } = useVaccineForm(vaccine, onOpenChange);

  const { data: availableVaccines } = useAvailableVaccines(vaccine?.id, open);
  const { data: existingRelationships } = useExistingRelationships(vaccine?.id, open);

  useEffect(() => {
    if (existingRelationships) {
      const relationshipMap = new Map();
      existingRelationships.forEach(rel => {
        relationshipMap.set(rel.child_vaccine_id, {
          id: rel.child_vaccine_id,
          doseNumber: rel.dose_number,
          doseType: rel.dose_type || 'dose'
        });
      });
      setSelectedVaccines(relationshipMap);
    } else {
      setSelectedVaccines(new Map());
    }
  }, [existingRelationships, setSelectedVaccines]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{vaccine ? "Editar Vacina" : "Nova Vacina"}</DialogTitle>
          <DialogDescription>
            Preencha os dados da vacina e selecione as vacinas relacionadas.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Nome da Vacina</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>
          <div>
            <Label>Vacinas Relacionadas</Label>
            <ScrollArea className="h-[200px] w-full border rounded-md p-4">
              <div className="space-y-4">
                {availableVaccines?.map((v) => (
                  <VaccineRelatedForm
                    key={v.id}
                    vaccineId={v.id}
                    vaccineName={v.name}
                    isSelected={selectedVaccines.has(v.id)}
                    relatedVaccine={selectedVaccines.get(v.id)}
                    onSelectionChange={handleVaccineSelection}
                    onDoseNumberChange={handleDoseNumberChange}
                    onDoseTypeChange={handleDoseTypeChange}
                  />
                ))}
              </div>
            </ScrollArea>
          </div>
          <Button type="submit" className="w-full">
            {vaccine ? "Atualizar" : "Criar"} Vacina
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}