
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import type { SearchResult } from "../types";
import { toxidromes } from "@/data/toxidromes";

export const useSearchResults = (searchTerm: string) => {
  const { toast } = useToast();

  const { data: searchResults = [], isLoading } = useQuery({
    queryKey: ['search', searchTerm],
    queryFn: async () => {
      // Verificar se o termo de busca tem pelo menos 3 caracteres
      if (!searchTerm || searchTerm.length < 3) return [];

      try {
        // Normalizar termo de busca para remover acentos e caracteres especiais
        const normalizedTerm = searchTerm.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();

        // Slugificar o termo para buscar por slug
        const slugifiedTerm = searchTerm.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");

        // Busca medicamentos (otimizada com busca de texto completo)
        const { data: medications, error: medicationsError } = await supabase
          .from('pedbook_medications')
          .select(`
            id,
            name,
            brands,
            slug,
            pedbook_medication_categories (
              id,
              name
            )
          `)
          .or(`name.ilike.%${searchTerm}%,brands.ilike.%${searchTerm}%,slug.eq.${slugifiedTerm}`)
          .order('name')
          .limit(20); // Limitar resultados para melhor performance

        if (medicationsError) throw medicationsError;

        // Busca categorias (otimizada com LIMIT)
        const { data: categories, error: categoriesError } = await supabase
          .from('pedbook_medication_categories')
          .select('id, name')
          .ilike('name', `%${searchTerm}%`)
          .order('name')
          .limit(10); // Limitar categorias

        if (categoriesError) throw categoriesError;

        // Busca CID-10 (otimizada com LIMIT)
        const { data: icd10, error: icd10Error } = await supabase
          .from('unified_cids')
          .select('id, code, name, description')
          .or(`name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%`)
          .order('code')
          .limit(15); // Limitar CIDs

        if (icd10Error) throw icd10Error;

        // Busca condutas/resumos (otimizada com LIMIT)
        const { data: conducts, error: conductsError } = await supabase
          .from('pedbook_conducts_summaries')
          .select(`
            id,
            title,
            slug,
            topic_id,
            pedbook_conducts_topics!inner (
              id,
              name,
              slug,
              category_id,
              pedbook_conducts_categories!inner (
                id,
                name,
                slug
              )
            )
          `)
          .or(`title.ilike.%${searchTerm}%,slug.eq.${slugifiedTerm}`)
          .order('title')
          .limit(15); // Limitar condutas

        if (conductsError) throw conductsError;

        // Busca toxidromes (otimizada com slice)
        const toxidromeResults = toxidromes
          .filter(toxidrome => {
            const searchLower = searchTerm.toLowerCase();
            const nameMatch = toxidrome.name.toLowerCase().includes(searchLower);
            const antidoteMatch = toxidrome.antidote.toLowerCase().includes(searchLower);
            return nameMatch || antidoteMatch;
          })
          .slice(0, 10) // Limitar toxidromes
          .map(toxidrome => ({
            id: toxidrome.id,
            name: toxidrome.name,
            type: 'toxidrome' as const,
            path: `/poisonings/${toxidrome.id}`,
            description: `Antídoto: ${toxidrome.antidote}`
          }));

        // Adiciona calculadoras
        const calculators = [
          { id: 'apgar', name: 'Calculadora de Apgar', path: '/calculadoras/apgar' },
          { id: 'rodwell', name: 'Calculadora de Rodwell', path: '/calculadoras/rodwell' },
          { id: 'capurro', name: 'Calculadora de Capurro', path: '/calculadoras/capurro' },
          { id: 'capurro-neuro', name: 'Calculadora de Capurro Neurológico', path: '/calculadoras/capurro-neuro' },
          { id: 'finnegan', name: 'Calculadora de Finnegan', path: '/calculadoras/finnegan' },
          { id: 'gina', name: 'Calculadora GINA', path: '/calculadoras/gina' },
          { id: 'glasgow', name: 'Calculadora de Glasgow', path: '/calculadoras/glasgow' },
          { id: 'bmi', name: 'Calculadora de IMC', path: '/calculadoras/imc' },
        ].filter(calc =>
          calc.name.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 8) // Limitar calculadoras
        .map(calc => ({
          ...calc,
          type: 'calculator' as const
        }));

        // Adiciona fluxogramas
        const flowcharts = [
          { id: 'dengue', name: 'Fluxograma de Dengue', path: '/flowcharts/dengue' },
          { id: 'asthma', name: 'Fluxograma de Asma', path: '/flowcharts/asthma' },
          { id: 'dka', name: 'Fluxograma de Cetoacidose Diabética', path: '/flowcharts/dka' },
          { id: 'anaphylaxis', name: 'Fluxograma de Anafilaxia', path: '/flowcharts/anaphylaxis' },
          { id: 'hydration', name: 'Hidratação Venosa de Manutenção', path: '/calculadoras/hidratacao' },
          // Fluxogramas de animais peçonhentos com nomes comuns
          { id: 'scorpion', name: 'Fluxograma de Acidente Escorpiônico (Escorpião Amarelo)', path: '/flowcharts/venomous/scorpion' },
          { id: 'bothropic', name: 'Fluxograma de Acidente Botrópico (Jararaca)', path: '/flowcharts/venomous/bothropic' },
          { id: 'crotalic', name: 'Fluxograma de Acidente Crotálico (Cascavel)', path: '/flowcharts/venomous/crotalic' },
          { id: 'elapidic', name: 'Fluxograma de Acidente Elapídico (Coral Verdadeira)', path: '/flowcharts/venomous/elapidic' },
          { id: 'phoneutria', name: 'Fluxograma de Acidente Fonêutrico (Aranha Armadeira)', path: '/flowcharts/venomous/phoneutria' },
          { id: 'loxoscelic', name: 'Fluxograma de Acidente Loxoscélico (Aranha Marrom)', path: '/flowcharts/venomous/loxoscelic' }
        ].filter(flow =>
          flow.name.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 10) // Limitar fluxogramas
        .map(flow => ({
          ...flow,
          type: 'flowchart' as const
        }));

        // Adiciona itens de puericultura
        const childcareItems = [
          { id: 'growth', name: 'Curvas de Crescimento', path: '/puericultura/curva-de-crescimento' },
          { id: 'vaccines', name: 'Vacinas', path: '/puericultura/calendario-vacinal' },
          { id: 'formulas', name: 'Fórmulas Infantis', path: '/puericultura/formulas' },
          { id: 'supplementation', name: 'Suplementação', path: '/puericultura/suplementacao-infantil' },
        ].filter(item =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5) // Limitar puericultura
        .map(item => ({
          ...item,
          type: 'childcare' as const
        }));

        const formattedResults: SearchResult[] = [
          ...(medications?.map(med => ({
            id: med.id,
            name: med.name,
            brands: med.brands,
            category: med.pedbook_medication_categories,
            type: 'medication' as const,
            slug: med.slug
          })) || []),
          ...(categories?.map(cat => ({
            id: cat.id,
            name: cat.name,
            type: 'category' as const
          })) || []),
          ...toxidromeResults,
          ...calculators,
          ...flowcharts,
          ...childcareItems,
          ...(conducts?.map(conduct => ({
            id: conduct.id,
            name: conduct.title,
            type: 'conduct' as const,
            path: `/condutas-e-manejos/${conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug}/${conduct.pedbook_conducts_topics.slug}`,
            description: `${conduct.pedbook_conducts_topics.pedbook_conducts_categories.name} > ${conduct.pedbook_conducts_topics.name}`,
            parent_slug: conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug
          })) || []),
          ...(icd10?.map(icd => ({
            id: icd.id,
            name: icd.name,
            code_range: icd.code,
            description: icd.description,
            type: 'icd10' as const
          })) || [])
        ];

        return formattedResults;
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Erro na busca",
          description: "Não foi possível completar a busca. Tente novamente."
        });
        return [];
      }
    },
    enabled: searchTerm.length >= 3, // Só executa a query quando tiver pelo menos 3 caracteres
    staleTime: 1000 * 60 * 5, // 5 minutos (mais razoável)
    gcTime: 1000 * 60 * 30, // 30 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Não refetch ao montar se já tem cache
    retry: 1, // Apenas 1 tentativa em caso de erro
    retryDelay: 1000 // 1 segundo de delay entre tentativas
  });

  return { searchResults, isLoading };
};
