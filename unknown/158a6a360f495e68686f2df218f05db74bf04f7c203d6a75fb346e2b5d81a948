import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PatientDataForm } from "./analysis/PatientDataForm";
import { GrowthChartTabs } from "./analysis/GrowthChartTabs";
import { useGrowthData } from "./hooks/useGrowthData";
import { Scale, Ruler, LineChart, Brain } from "lucide-react";

interface PatientData {
  age: number;
  gender: 'male' | 'female';
  weight?: number;
  height?: number;
  headCircumference?: number;
  birthWeight?: number;
  maturity?: 'Term' | 'Pre-term';
}

interface PatientAnalysisProps {
  initialData?: PatientData;
  readOnly?: boolean;
}

export function PatientAnalysis({ initialData, readOnly = false }: PatientAnalysisProps) {
  const [patientData, setPatientData] = useState<PatientData>(initialData ?? {
    age: 0,
    gender: 'male',
  });
  const [showCharts, setShowCharts] = useState(!!initialData);
  const { toast } = useToast();

  const { weightData, heightData, bmiData, headCircumferenceData } = useGrowthData({
    gender: patientData.gender,
    enabled: showCharts,
  });

  const handleAnalyze = () => {
    if (!patientData.age || !patientData.gender) {
      toast({
        title: "Dados incompletos",
        description: "Idade e gênero são obrigatórios para a análise.",
        variant: "destructive",
      });
      return;
    }
    setShowCharts(true);
  };

  return (
    <div className="space-y-8">
      {!readOnly && (
        <Card className="p-6 glass-card animate-fade-in-up">
          <PatientDataForm
            data={patientData}
            onChange={setPatientData}
          />

          <div className="mt-6 flex justify-end">
            <Button 
              onClick={handleAnalyze}
              className="bg-gradient-to-r from-primary to-primary/70 hover:opacity-90 transition-all"
            >
              <LineChart className="mr-2 h-5 w-5" />
              Analisar
            </Button>
          </div>
        </Card>
      )}

      {showCharts && (
        <GrowthChartTabs
          patientData={patientData}
          chartData={{
            weightData,
            heightData,
            bmiData,
            headCircumferenceData
          }}
        />
      )}
    </div>
  );
}