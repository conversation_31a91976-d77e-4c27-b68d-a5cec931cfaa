import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Formula } from "../types";

interface FormulaBrandsProps {
  formulas: Formula[] | undefined;
  selectedFormula: Formula | null;
  setSelectedFormula: (formula: Formula | null) => void;
}

export const FormulaBrands = ({ 
  formulas, 
  selectedFormula, 
  setSelectedFormula 
}: FormulaBrandsProps) => {
  return (
    <div className="flex flex-wrap gap-3 justify-center">
      {formulas?.map((formula) => (
        <motion.div
          key={formula.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="flex-shrink-0"
        >
          <Card
            className={cn(
              "p-4 cursor-pointer transition-all duration-300 hover:shadow-md bg-white/80 backdrop-blur-sm border-2",
              selectedFormula?.id === formula.id 
                ? "border-primary shadow-lg transform -translate-y-1" 
                : "border-transparent hover:-translate-y-1"
            )}
            onClick={() => setSelectedFormula(
              selectedFormula?.id === formula.id ? null : formula
            )}
          >
            <div className="space-y-2 text-center">
              <h4 className="font-medium text-gray-900 whitespace-nowrap">{formula.brand}</h4>
              <p className="text-sm text-gray-600 whitespace-nowrap">{formula.name}</p>
            </div>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};