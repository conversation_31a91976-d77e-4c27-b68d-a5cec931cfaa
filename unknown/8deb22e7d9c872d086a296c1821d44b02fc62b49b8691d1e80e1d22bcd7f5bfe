
import { ArrowLeft, User, Calendar, Weight, Ruler, Baby, Brain, Activity } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { formatRelativeAge } from "@/utils/formatRelativeAge";

interface PatientSummaryHeaderProps {
  patientData: {
    name?: string;
    birthDate?: string;
    age: number;
    gender: "male" | "female";
    weight?: number;
    height?: number;
    maturity: "Term" | "Pre-term";
    exclusiveBreastfeeding?: boolean;
    hasRiskFactors?: boolean;
  };
  onBack: () => void;
}

export function PatientSummaryHeader({ patientData, onBack }: PatientSummaryHeaderProps) {
  console.log("👤 Renderizando cabeçalho do resumo do paciente");

  const getGenderColor = () => {
    return patientData.gender === "male" 
      ? "bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-400" 
      : "bg-pink-100 text-pink-600 dark:bg-pink-900/40 dark:text-pink-400";
  };

  const formatMaturityLabel = () => {
    if (patientData.maturity === "Term") {
      return "A termo";
    } else {
      return "Pré-termo";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          onClick={onBack} 
          className="bg-white hover:bg-primary/5 border-primary/20 hover:border-primary/30 transition-all group dark:bg-slate-800 dark:border-slate-700"
        >
          <ArrowLeft className="mr-2 h-5 w-5 text-primary/70 group-hover:translate-x-[-2px] transition-transform dark:text-blue-400" />
          <span className="text-primary/80 font-medium dark:text-blue-400">Nova avaliação</span>
        </Button>
      </div>

      <Card className="p-6 bg-white dark:bg-slate-800 border border-primary/10 shadow-md">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {/* Identificação do Paciente */}
          <div className="md:col-span-2 flex flex-col justify-between space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 flex items-center justify-center bg-primary/10 dark:bg-primary/20 rounded-full">
                <User className="h-8 w-8 text-primary dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                  {patientData.name || "Paciente"}
                </h2>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGenderColor()}`}>
                    {patientData.gender === "male" ? "Masculino" : "Feminino"}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                    {formatMaturityLabel()}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-primary/5 dark:bg-primary/10 rounded-lg">
              <Calendar className="h-5 w-5 text-primary/70 dark:text-blue-400" />
              <div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Idade</div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {formatRelativeAge(patientData.age)} 
                  <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                    ({patientData.age} meses)
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Separador vertical */}
          <div className="hidden md:block border-r border-gray-200 dark:border-gray-700"></div>

          {/* Medidas e Status */}
          <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-3">
            {patientData.weight && (
              <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <Weight className="h-5 w-5 text-orange-500 dark:text-orange-400" />
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Peso</div>
                  <div className="font-medium text-gray-900 dark:text-white">{patientData.weight} kg</div>
                </div>
              </div>
            )}

            {patientData.height && (
              <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Ruler className="h-5 w-5 text-green-500 dark:text-green-400" />
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Estatura</div>
                  <div className="font-medium text-gray-900 dark:text-white">{patientData.height} cm</div>
                </div>
              </div>
            )}

            {patientData.exclusiveBreastfeeding !== undefined && (
              <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Baby className="h-5 w-5 text-purple-500 dark:text-purple-400" />
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Amamentação</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {patientData.exclusiveBreastfeeding ? "Exclusiva" : "Não exclusiva"}
                  </div>
                </div>
              </div>
            )}

            {patientData.hasRiskFactors !== undefined && (
              <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Activity className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Fatores de risco</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {patientData.hasRiskFactors ? "Presentes" : "Ausentes"}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
