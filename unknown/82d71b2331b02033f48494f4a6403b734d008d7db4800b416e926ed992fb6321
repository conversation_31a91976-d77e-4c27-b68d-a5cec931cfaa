
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * Hook para interceptar o botão de voltar físico do dispositivo móvel
 * e navegar dentro do aplicativo em vez de sair dele
 */
export const useBackButton = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Definir as rotas principais do aplicativo
    const mainRoutes = [
      "/", // Home
      "/medicamentos",
      "/medicamentos/painel",
      "/puericultura",
      "/dr-will",
      "/calculadoras",
      "/flowcharts",
      "/condutas",
      "/bulas",
      "/interacoes",
      "/cid",
      "/plataformadeestudos",
      "/feedback",
      "/perfil",
      "/busca",
      "/configuracoes",
      "/newsletters", // Notícias Diárias
    ];

    // Função para lidar com o evento de voltar do navegador/dispositivo
    const handleBackButton = (event: PopStateEvent) => {
      // Se estiver na página inicial, não interceptamos o comportamento padrão
      if (location.pathname === '/') {
        return;
      }

      // Previne o comportamento padrão
      event.preventDefault();

      // Se estamos em uma rota principal, navegar para a home
      if (mainRoutes.includes(location.pathname) && location.pathname !== "/") {
        navigate("/");
        window.history.pushState(null, '', "/");
        return;
      }

      // Se estamos em uma subrota, encontrar a rota principal correspondente
      for (const route of mainRoutes) {
        // Ignorar a rota raiz para evitar falsos positivos
        if (route !== "/" && location.pathname.startsWith(route + "/")) {
          navigate(route);
          window.history.pushState(null, '', route);
          return;
        }
      }

      // Se não encontramos uma rota principal correspondente, navegar para trás
      navigate(-1);

      // Adiciona uma nova entrada ao histórico para garantir que a navegação continue funcionando
      window.history.pushState(null, '', location.pathname);
    };

    // Garante que temos uma entrada no histórico para o estado atual
    window.history.pushState(null, '', location.pathname);

    // Adiciona um listener para o evento popstate (botão voltar)
    window.addEventListener('popstate', handleBackButton);

    // Limpa o listener quando o componente é desmontado
    return () => {
      window.removeEventListener('popstate', handleBackButton);
    };
  }, [location.pathname, navigate]);
};
