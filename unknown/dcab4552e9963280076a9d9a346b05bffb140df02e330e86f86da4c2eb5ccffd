
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";

export const EmptyResults = ({ searchTerm }: { searchTerm?: string }) => {
  const navigate = useNavigate();

  const handleAskWill = () => {
    navigate('/dr-will', { state: { initialMessage: searchTerm } });
  };

  // Mensagem específica para termos de busca muito curtos
  if (searchTerm && searchTerm.length < 3) {
    return (
      <div className="p-8 text-center">
        <div className="bg-gray-100/80 dark:bg-slate-700/80 rounded-lg p-4 inline-block mb-2">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Digite pelo menos 3 caracteres para buscar
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 text-center">
      <div className="bg-gray-100/80 dark:bg-slate-700/80 rounded-lg p-6 max-w-xs mx-auto">
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
          Nenhum resultado encontrado
        </p>
        <p className="text-sm font-medium text-primary dark:text-blue-400 mb-4">
          Que tal perguntar ao Dr. Will?
        </p>
        <Button
          variant="default"
          onClick={handleAskWill}
          className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300 rounded-md"
        >
          <MessageCircle className="mr-2 h-4 w-4" />
          Perguntar ao Will
        </Button>
      </div>
    </div>
  );
};
