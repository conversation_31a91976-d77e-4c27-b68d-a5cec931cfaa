
import React, { useEffect } from "react";
import { SearchBar } from "@/components/search/SearchBar";
import Ped<PERSON>ook<PERSON>ogo from "./PedBookLogo";

interface SearchSectionProps {
  searchPlaceholder: string;
}

export const SearchSection: React.FC<SearchSectionProps> = ({ searchPlaceholder }) => {
  useEffect(() => {
    // console.log("🔍 [SearchSection] Component rendered");
  }, []);

  return (
    <div className="w-full max-w-6xl mx-auto relative z-10 flex items-center gap-2 px-2">
      <div className="flex-1 relative group">
        <SearchBar customPlaceholder={searchPlaceholder || "Buscar medicamentos, condutas, calculadoras..."} />
      </div>
    </div>
  );
};
