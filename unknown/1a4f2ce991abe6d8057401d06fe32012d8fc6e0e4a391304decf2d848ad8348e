import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Scale, Ruler, LineChart, Brain } from "lucide-react";
import { GrowthChart } from "./GrowthChart";

interface PatientData {
  age: number;
  gender: 'male' | 'female';
  weight?: number;
  height?: number;
  headCircumference?: number;
}

interface GrowthChartTabsProps {
  patientData: PatientData;
  chartData: {
    weightData: any;
    heightData: any;
    bmiData: any;
    headCircumferenceData: any;
  };
}

export function GrowthChartTabs({ patientData, chartData }: GrowthChartTabsProps) {
  const getChartData = (type: 'weight' | 'height' | 'bmi' | 'head-circumference') => {
    const data = type === 'weight' ? chartData.weightData : 
                type === 'height' ? chartData.heightData : 
                type === 'bmi' ? chartData.bmiData : 
                chartData.headCircumferenceData;
    
    if (!data?.data || !Array.isArray(data.data)) return [];
    
    const patientBMI = patientData.weight && patientData.height ? 
      (patientData.weight / Math.pow(patientData.height / 100, 2)) : 
      undefined;
    
    return data.data.map((point: any) => ({
      age: point.age_months,
      p3: point.percentiles["3rd"],
      p15: point.percentiles["15th"],
      p50: point.percentiles["50th"],
      p85: point.percentiles["85th"],
      p97: point.percentiles["97th"],
      patient: point.age_months === patientData.age ? 
        (type === 'weight' ? patientData.weight : 
         type === 'height' ? patientData.height : 
         type === 'bmi' ? patientBMI :
         patientData.headCircumference) : 
        undefined,
    }));
  };

  const content = (
    <Tabs defaultValue="weight" className="w-full">
      <div className="space-y-2 mb-6">
        <h3 className="text-sm text-center text-gray-500 font-light">
          Escolha uma curva de crescimento
        </h3>
        <TabsList className="grid w-full grid-cols-4 gap-1 bg-white/50 rounded-lg p-1 border border-primary/10">
          <TabsTrigger 
            value="weight" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Scale className="w-4 h-4 mr-1.5" />
            Peso
          </TabsTrigger>
          <TabsTrigger 
            value="height" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Ruler className="w-4 h-4 mr-1.5" />
            Altura
          </TabsTrigger>
          <TabsTrigger 
            value="bmi" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <LineChart className="w-4 h-4 mr-1.5" />
            IMC
          </TabsTrigger>
          <TabsTrigger 
            value="head" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Brain className="w-4 h-4 mr-1.5" />
            PC
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="weight" className="mt-0">
        {chartData.weightData && patientData.weight && (
          <div className="w-full">
            <h3 className="text-lg font-semibold text-center text-gray-800 mb-4">Curva de Peso</h3>
            <GrowthChart
              data={getChartData('weight')}
              measurementType="weight"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="height" className="mt-0">
        {chartData.heightData && patientData.height && (
          <div className="w-full">
            <h3 className="text-lg font-semibold text-center text-gray-800 mb-4">Curva de Altura</h3>
            <GrowthChart
              data={getChartData('height')}
              measurementType="height"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="bmi" className="mt-0">
        {chartData.bmiData && patientData.weight && patientData.height && (
          <div className="w-full">
            <h3 className="text-lg font-semibold text-center text-gray-800 mb-4">Curva de IMC</h3>
            <GrowthChart
              data={getChartData('bmi')}
              measurementType="bmi"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="head" className="mt-0">
        {chartData.headCircumferenceData && patientData.headCircumference && (
          <div className="w-full">
            <h3 className="text-lg font-semibold text-center text-gray-800 mb-4">Curva de Perímetro Cefálico</h3>
            <GrowthChart
              data={getChartData('head-circumference')}
              measurementType="head-circumference"
            />
          </div>
        )}
      </TabsContent>
    </Tabs>
  );

  return (
    <>
      <div className="md:hidden">
        {content}
      </div>
      <div className="hidden md:block">
        <Card className="p-6 glass-card animate-fade-in-up">
          {content}
        </Card>
      </div>
    </>
  );
}