import React, { useRef, useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Chart } from "chart.js/auto";

export const BhutaniForm = () => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);
  const [result, setResult] = useState<string>("");
  const [continuousEntry, setContinuousEntry] = useState(false);

  // Dados completos dos percentis
  const dataPointsP40 = [4, 5, 7, 8.5, 9.5, 11.1, 11.6, 12.5, 13, 13.3, 13.2, 13.1];
  const dataPointsP75 = [5, 6, 9, 10.9, 12.8, 13.6, 14.8, 15.2, 15.6, 15.9, 15.5, 15.2];
  const dataPointsP95 = [7, 8, 11, 13.1, 15.1, 16, 16.8, 17.2, 17.2, 17.3, 17.2, 17.1];

  const interpolate = (dataPoints: number[]) => {
    const result = [];
    for (let i = 12; i <= 144; i++) {
      const index = (i - 12) / 12;
      if (Number.isInteger(index) && index < dataPoints.length) {
        result.push(dataPoints[index]);
      } else {
        const lowerIndex = Math.floor(index);
        const upperIndex = Math.ceil(index);
        if (upperIndex < dataPoints.length) {
          const weight = index - lowerIndex;
          result.push(dataPoints[lowerIndex] * (1 - weight) + dataPoints[upperIndex] * weight);
        } else {
          result.push(dataPoints[dataPoints.length - 1]);
        }
      }
    }
    return result;
  };

  useEffect(() => {
    if (chartRef.current) {
      const ctx = chartRef.current.getContext("2d");
      if (ctx) {
        if (chartInstance.current) {
          chartInstance.current.destroy();
        }

        chartInstance.current = new Chart(ctx, {
          type: "line",
          data: {
            labels: Array.from({ length: 133 }, (_, i) => i + 12),
            datasets: [
              {
                label: "P40 (mg/dL)",
                data: interpolate(dataPointsP40),
                borderColor: "blue",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "P75 (mg/dL)",
                data: interpolate(dataPointsP75),
                borderColor: "green",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "P95 (mg/dL)",
                data: interpolate(dataPointsP95),
                borderColor: "red",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "Bilirrubina Total (mg/dL)",
                data: [],
                borderColor: "orange",
                pointBackgroundColor: "orange",
                borderWidth: 2,
                fill: false,
                pointRadius: 5,
                showLine: continuousEntry
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              x: {
                type: "linear",
                position: "bottom",
                min: 12,
                max: 144,
                ticks: {
                  stepSize: 12
                }
              },
              y: {
                beginAtZero: true,
                max: 20
              },
            },
          },
        });
      }
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [continuousEntry]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const age = parseFloat(formData.get("age") as string);
    const bilirubin = parseFloat(formData.get("bilirubin") as string);

    if (age < 12 || age > 144 || isNaN(bilirubin)) {
      alert("Idade deve estar entre 12 e 144 horas e bilirrubina deve ser válida.");
      return;
    }

    if (chartInstance.current) {
      const chart = chartInstance.current;
      const currentData = [...chart.data.datasets[3].data];

      if (!continuousEntry) {
        // Se não estiver em modo contínuo, limpa os pontos anteriores
        currentData.length = 0;
      }
      
      currentData.push({ x: age, y: bilirubin });
      
      // Ordenar os pontos por idade para garantir que a linha seja desenhada corretamente
      currentData.sort((a: any, b: any) => a.x - b.x);
      
      chart.data.datasets[3].data = currentData;
      chart.update();

      const index = Math.floor(age - 12);
      const p40Value = interpolate(dataPointsP40)[index];
      const p75Value = interpolate(dataPointsP75)[index];
      const p95Value = interpolate(dataPointsP95)[index];

      let riskZone = "";
      if (bilirubin > p95Value) {
        riskZone = "Zona de Alto Risco";
      } else if (bilirubin > p75Value) {
        riskZone = "Zona de Risco Intermediário Alto";
      } else if (bilirubin > p40Value) {
        riskZone = "Zona de Risco Intermediário Baixo";
      } else {
        riskZone = "Zona de Baixo Risco";
      }

      setResult(riskZone);

      // Se não estiver em modo contínuo, limpar os campos
      if (!continuousEntry) {
        (e.target as HTMLFormElement).reset();
      }
    }
  };

  const handleClear = () => {
    if (chartInstance.current) {
      chartInstance.current.data.datasets[3].data = [];
      chartInstance.current.update();
      setResult("");
    }
  };

  return (
    <Card className="p-6 space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="age">Idade (horas)</Label>
            <Input
              id="age"
              name="age"
              type="number"
              min="12"
              max="144"
              step="1"
              required
              placeholder="Entre 12 e 144 horas"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="bilirubin">Bilirrubina Total (mg/dL)</Label>
            <Input
              id="bilirubin"
              name="bilirubin"
              type="number"
              step="0.1"
              required
              placeholder="Valor da bilirrubina"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="continuousEntry"
            checked={continuousEntry}
            onCheckedChange={(checked) => setContinuousEntry(checked as boolean)}
          />
          <Label htmlFor="continuousEntry">Entrada contínua de dados</Label>
        </div>
        <div className="flex gap-2 justify-center">
          <Button type="submit" className="bg-primary">
            Adicionar ponto
          </Button>
          <Button type="button" variant="outline" onClick={handleClear}>
            Limpar gráfico
          </Button>
        </div>
      </form>

      <div className="h-[400px] md:h-[300px]"> {/* Altura fixa para garantir consistência */}
        <canvas ref={chartRef}></canvas>
      </div>

      {result && (
        <div className="text-center font-semibold text-lg">
          <span className={`
            ${result.includes("Alto Risco") ? "text-red-600" : ""}
            ${result.includes("Intermediário Alto") ? "text-orange-600" : ""}
            ${result.includes("Intermediário Baixo") ? "text-yellow-600" : ""}
            ${result.includes("Baixo Risco") ? "text-green-600" : ""}
          `}>
            {result}
          </span>
          {result.includes("Alto Risco") && (
            <div className="text-red-600 mt-2">
              Conduta: Iniciar fototerapia imediatamente. Reavaliar BT em 4 a 6 horas. OBS: Se BT permanecer em nível crítico considere exsanguineotransfusão em casos de progressão ou sinais de kernicterus.
            </div>
          )}
          {result.includes("Intermediário Alto") && (
            <div className="text-orange-600 mt-2">
              Conduta: Monitorar BT sérica em 12 a 24 horas. Considerar fototerapia dependendo da idade gestacional e fatores de risco associados.
            </div>
          )}
          {result.includes("Intermediário Baixo") && (
            <div className="text-yellow-600 mt-2">
              Conduta: Acompanhar clinicamente. Monitorar BT dependendo da evolução clínica e fatores de risco. Garantir aleitamento materno eficaz.
            </div>
          )}
          {result.includes("Baixo Risco") && (
            <div className="text-green-600 mt-2">
              Conduta: Seguimento ambulatorial de rotina. Educação dos pais sobre sinais de alerta.
            </div>
          )}
        </div>
      )}
    </Card>
  );
};
