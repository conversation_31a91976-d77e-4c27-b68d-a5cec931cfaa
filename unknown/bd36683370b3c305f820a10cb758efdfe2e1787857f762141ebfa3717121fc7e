import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, ChevronDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface MobileMedicationSelectorProps {
  categories: Array<{
    id: string;
    name: string;
    pedbook_medications: Array<{
      id: string;
      slug: string;
      name: string;
    }>;
  }> | undefined;
  currentMedicationId: string;
  onMedicationSelect: (medicationId: string) => void;
}

export const MobileMedicationSelector = ({
  categories,
  currentMedicationId,
  onMedicationSelect,
}: MobileMedicationSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const currentMedication = categories?.flatMap(cat => cat.pedbook_medications)
    .find(med => med?.id === currentMedicationId);

  // Helper function to normalize text (remove accents and special characters)
  const normalizeText = (text: string) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase();
  };

  // Advanced search function for medications
  const searchMedication = (medication: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeText(searchTerm);
    const normalizedMedicationName = normalizeText(medication.name);

    // Search in medication name (normalized)
    if (normalizedMedicationName.includes(normalizedSearchTerm)) {
      return true;
    }

    // Search in brands/commercial names (normalized)
    if (medication.brands) {
      const normalizedBrands = normalizeText(medication.brands);
      if (normalizedBrands.includes(normalizedSearchTerm)) {
        return true;
      }
    }

    // Search in slug
    if (medication.slug) {
      const slugifiedSearchTerm = searchTerm.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");
      if (medication.slug.includes(slugifiedSearchTerm)) {
        return true;
      }
    }

    return false;
  };

  const filteredCategories = categories?.map(category => ({
    ...category,
    pedbook_medications: category.pedbook_medications?.filter(med =>
      searchMedication(med, searchTerm)
    )
  })).filter(category => category.pedbook_medications?.length > 0)
    .sort((a, b) => normalizeText(a.name).localeCompare(normalizeText(b.name))); // Ordena as categorias em ordem alfabética



  return (
    <div className="w-full bg-white rounded-xl shadow-sm border border-gray-100">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}
        className="w-full"
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <span className="font-medium">
              {currentMedication?.name || "Selecionar Medicamento"}
            </span>
            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? "transform rotate-180" : ""}`} />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="p-4 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar por nome ou marca comercial..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <ScrollArea className="h-[300px]">
            <div className="space-y-4">
              {searchTerm && filteredCategories?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="h-8 w-8 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">Nenhum medicamento encontrado</p>
                  <p className="text-xs mt-1">Tente buscar por nome ou marca comercial</p>
                </div>
              ) : (
                filteredCategories?.map((category) => (
                <div key={category.id} className="space-y-2">
                  <h3 className="font-medium text-sm text-primary/80 uppercase tracking-wider">
                    {category.name}
                  </h3>
                  <div className="space-y-1">
                    {category.pedbook_medications?.map((med) => (
                      <Button
                        key={med.id}
                        variant="ghost"
                        className={`w-full justify-start text-sm h-auto py-2 px-3 ${
                          med.id === currentMedicationId
                            ? "bg-primary text-primary-foreground hover:bg-primary/90"
                            : "hover:bg-primary/5"
                        }`}
                        onClick={() => {
                          console.log('🔗 [MobileSelector] Medication selected:', med.slug, med.name);
                          onMedicationSelect(med.slug);
                          setIsOpen(false);
                        }}
                      >
                        <div className="text-left">
                          <div className="font-medium">{med.name}</div>
                          {searchTerm && med.brands && normalizeText(med.brands).includes(normalizeText(searchTerm)) && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Marcas: {med.brands}
                            </div>
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};