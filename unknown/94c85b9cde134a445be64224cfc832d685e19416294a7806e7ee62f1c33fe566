import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Scale, Ruler, Brain } from "lucide-react";

export function MeasurementsInputs() {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="birthWeight" className="flex items-center gap-2">
          <Scale className="h-4 w-4" />
          Peso ao Nascer (g)
        </Label>
        <Input
          id="birthWeight"
          name="birthWeight"
          type="number"
          step="1"
          min={1000}
          required
          className="bg-white"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="weight" className="flex items-center gap-2">
          <Scale className="h-4 w-4" />
          Peso Atual (kg)
        </Label>
        <Input
          id="weight"
          name="weight"
          type="number"
          step="0.01"
          min={0}
          max={100}
          required
          className="bg-white"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="height" className="flex items-center gap-2">
          <Ruler className="h-4 w-4" />
          Altura (cm)
        </Label>
        <Input
          id="height"
          name="height"
          type="number"
          step="0.1"
          min={0}
          required
          className="bg-white"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="headCircumference" className="flex items-center gap-2">
          <Brain className="h-4 w-4" />
          Perímetro Cefálico (cm)
        </Label>
        <Input
          id="headCircumference"
          name="headCircumference"
          type="number"
          step="0.1"
          min={0}
          required
          className="bg-white"
        />
      </div>
    </>
  );
}