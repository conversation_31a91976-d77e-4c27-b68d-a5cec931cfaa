
import { Card } from "@/components/ui/card";
import { calculateSupplementation } from "@/utils/supplementationCalculator";
import { SupplementationInput } from "@/types/supplementation";
import SupplementationFAQ from "@/components/supplementation/SupplementationFAQ";

interface SupplementationAnalysisProps {
  data: {
    age: number;
    weight?: number;
    birthWeight?: number;
    maturity: 'Term' | 'Pre-term';
    exclusiveBreastfeeding: boolean;
    hasRiskFactors: boolean;
  };
}

export const SupplementationAnalysis = ({ data }: SupplementationAnalysisProps) => {
  const supplementationInput: SupplementationInput = {
    ageInDays: Math.round(data.age * 30.44), // Convert months to days
    currentWeight: data.weight ? data.weight * 1000 : 0, // Convert kg to grams
    birthWeight: data.birthWeight || 0,
    maturity: data.maturity,
    exclusiveBreastfeeding: data.exclusiveBreastfeeding,
    hasRiskFactors: data.hasRiskFactors
  };

  const recommendations = calculateSupplementation(supplementationInput);
  
  console.log('🌙 Renderizando SupplementationAnalysis com suporte a dark mode');

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <Card className="p-4 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800/50">
          <h3 className="font-semibold text-amber-700 dark:text-amber-300 mb-2">Vitamina A</h3>
          <p className="text-amber-600 dark:text-amber-200/90">{recommendations.vitaminA}</p>
        </Card>
        
        <Card className="p-4 bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50">
          <h3 className="font-semibold text-purple-700 dark:text-purple-300 mb-2">Vitamina D</h3>
          <p className="text-purple-600 dark:text-purple-200/90">{recommendations.vitaminD}</p>
        </Card>
        
        <Card className="p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800/50">
          <h3 className="font-semibold text-red-700 dark:text-red-300 mb-2">Ferro</h3>
          <p className="text-red-600 dark:text-red-200/90">{recommendations.iron}</p>
        </Card>
      </div>

      <div className="mt-8">
        <SupplementationFAQ />
      </div>
    </div>
  );
};
