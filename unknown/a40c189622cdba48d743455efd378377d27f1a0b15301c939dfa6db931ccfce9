-- Fix Search Path Security Issues for All Functions
-- This script adds SET search_path = public to all functions with mutable search_path

-- 1. get_filtered_question_count
CREATE OR REPLACE FUNCTION public.get_filtered_question_count(
  domain_filter text DEFAULT NULL,
  specialty_ids text[] DEFAULT NULL,
  theme_ids text[] DEFAULT NULL,
  focus_ids text[] DEFAULT NULL,
  location_ids text[] DEFAULT NULL,
  years integer[] DEFAULT NULL,
  question_types text[] DEFAULT NULL,
  question_formats text[] DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
DECLARE
  total_count integer;
  has_category_filters boolean;
BEGIN
  -- Check if any category filters are active
  has_category_filters := (
      (specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0) OR
      (theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0) OR
      (focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0)
  );
  
  -- Count questions with all filters
  SELECT COUNT(*) INTO total_count
  FROM questions q
  WHERE 1=1
    -- Domain filter
    AND (domain_filter IS NULL OR q.knowledge_domain = domain_filter)
    -- Category filters (OR logic between them if present)
    AND (
        CASE WHEN has_category_filters THEN
            (
                CASE 
                    WHEN specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0 THEN q.specialty_id::text = ANY(specialty_ids)
                    ELSE FALSE
                END
                OR
                CASE 
                    WHEN theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0 THEN q.theme_id::text = ANY(theme_ids)
                    ELSE FALSE
                END
                OR
                CASE 
                    WHEN focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0 THEN q.focus_id::text = ANY(focus_ids)
                    ELSE FALSE
                END
            )
        ELSE TRUE
        END
    )
    -- Location filter (independent)
    AND (array_length(location_ids, 1) IS NULL OR q.exam_location::text = ANY(location_ids))
    -- Year filter (independent)
    AND (array_length(years, 1) IS NULL OR q.exam_year = ANY(years))
    -- Question type filter (independent)
    AND (array_length(question_types, 1) IS NULL OR q.assessment_type = ANY(question_types))
    -- Question format filter (independent)
    AND (array_length(question_formats, 1) IS NULL OR q.question_format::text = ANY(question_formats));

  RETURN json_build_object('total_count', total_count);
END;
$$;

-- 2. get_filtered_question_count_excluding_answered
CREATE OR REPLACE FUNCTION public.get_filtered_question_count_excluding_answered(
  user_id uuid DEFAULT NULL,
  domain_filter text DEFAULT NULL,
  specialty_ids text[] DEFAULT NULL,
  theme_ids text[] DEFAULT NULL,
  focus_ids text[] DEFAULT NULL,
  location_ids text[] DEFAULT NULL,
  years integer[] DEFAULT NULL,
  question_types text[] DEFAULT NULL,
  question_formats text[] DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
DECLARE
  total_count integer;
  has_category_filters boolean;
BEGIN
  -- Check if any category filters are active
  has_category_filters := (
      (specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0) OR
      (theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0) OR
      (focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0)
  );
  
  -- Count questions with all filters
  SELECT COUNT(*) INTO total_count
  FROM questions q
  WHERE 1=1
    -- Domain filter
    AND (domain_filter IS NULL OR q.knowledge_domain = domain_filter)
    -- Category filters (OR logic between them if present)
    AND (
        CASE WHEN has_category_filters THEN
            (
                CASE 
                    WHEN specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0 THEN q.specialty_id::text = ANY(specialty_ids)
                    ELSE FALSE
                END
                OR
                CASE 
                    WHEN theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0 THEN q.theme_id::text = ANY(theme_ids)
                    ELSE FALSE
                END
                OR
                CASE 
                    WHEN focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0 THEN q.focus_id::text = ANY(focus_ids)
                    ELSE FALSE
                END
            )
        ELSE TRUE
        END
    )
    -- Location filter (independent)
    AND (array_length(location_ids, 1) IS NULL OR q.exam_location::text = ANY(location_ids))
    -- Year filter (independent)
    AND (array_length(years, 1) IS NULL OR q.exam_year = ANY(years))
    -- Question type filter (independent)
    AND (array_length(question_types, 1) IS NULL OR q.assessment_type = ANY(question_types))
    -- Question format filter (independent)
    AND (array_length(question_formats, 1) IS NULL OR q.question_format::text = ANY(question_formats))
    -- Exclude answered questions if user_id is provided (FIXED: use parameter name explicitly)
    AND (get_filtered_question_count_excluding_answered.user_id IS NULL OR q.id NOT IN (
        SELECT DISTINCT ua.question_id 
        FROM user_answers ua 
        WHERE ua.user_id = get_filtered_question_count_excluding_answered.user_id 
        AND ua.is_correct = true
    ));

  RETURN json_build_object('total_count', total_count);
END;
$$;

-- 3. update_cards_state
CREATE OR REPLACE FUNCTION public.update_cards_state(
  p_card_ids uuid[],
  p_new_state text
)
RETURNS void
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
BEGIN
  UPDATE public.flashcards_cards 
  SET 
    current_state = p_new_state,
    updated_at = NOW()
  WHERE id = ANY(p_card_ids);
END;
$$;

-- 4. update_user_study_statistics
CREATE OR REPLACE FUNCTION public.update_user_study_statistics(
  p_user_id uuid
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
DECLARE
    v_streak_data RECORD;
    v_result JSONB;
BEGIN
    -- Calcular sequências atualizadas
    SELECT current_streak, max_streak 
    INTO v_streak_data
    FROM calculate_user_study_streak(p_user_id);
    
    -- Retornar resultado
    v_result := jsonb_build_object(
        'user_id', p_user_id,
        'current_streak', v_streak_data.current_streak,
        'max_streak', v_streak_data.max_streak,
        'updated_at', NOW()
    );
    
    RETURN v_result;
END;
$$;

-- 5. get_filtered_flashcards
CREATE OR REPLACE FUNCTION public.get_filtered_flashcards(
  p_user_id uuid,
  p_specialties uuid[] DEFAULT NULL,
  p_themes uuid[] DEFAULT NULL,
  p_focuses uuid[] DEFAULT NULL,
  p_extrafocuses uuid[] DEFAULT NULL,
  p_limit integer DEFAULT 50
)
RETURNS TABLE(
  id uuid,
  specialty_id uuid,
  theme_id uuid,
  focus_id uuid,
  extrafocus_id uuid
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fc.id,
    fc.specialty_id,
    fc.theme_id,
    fc.focus_id,
    fc.extrafocus_id
  FROM public.flashcards_cards fc
  WHERE fc.user_id = p_user_id 
    AND fc.current_state = 'available'
    AND (p_specialties IS NULL OR fc.specialty_id = ANY(p_specialties))
    AND (p_themes IS NULL OR fc.theme_id = ANY(p_themes))
    AND (p_focuses IS NULL OR fc.focus_id = ANY(p_focuses))
    AND (p_extrafocuses IS NULL OR fc.extrafocus_id = ANY(p_extrafocuses))
  ORDER BY fc.created_at DESC
  LIMIT p_limit;
END;
$$;

-- 6. get_hierarchical_location_counts
CREATE OR REPLACE FUNCTION public.get_hierarchical_location_counts(
  domain_filter text DEFAULT NULL,
  specialty_ids text[] DEFAULT NULL,
  theme_ids text[] DEFAULT NULL,
  focus_ids text[] DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
DECLARE
  result json;
  has_category_filters boolean;
BEGIN
  -- Check if any category filters are active
  has_category_filters := (
      (specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0) OR
      (theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0) OR
      (focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0)
  );

  -- Build location counts based on selected categories
  WITH filtered_questions AS (
    SELECT q.exam_location
    FROM questions q
    WHERE 1=1
      -- Domain filter
      AND (domain_filter IS NULL OR q.knowledge_domain = domain_filter)
      -- Category filters (OR logic between them if present)
      AND (
          CASE WHEN has_category_filters THEN
              (
                  CASE
                      WHEN specialty_ids IS NOT NULL AND array_length(specialty_ids, 1) > 0 THEN q.specialty_id::text = ANY(specialty_ids)
                      ELSE FALSE
                  END
                  OR
                  CASE
                      WHEN theme_ids IS NOT NULL AND array_length(theme_ids, 1) > 0 THEN q.theme_id::text = ANY(theme_ids)
                      ELSE FALSE
                  END
                  OR
                  CASE
                      WHEN focus_ids IS NOT NULL AND array_length(focus_ids, 1) > 0 THEN q.focus_id::text = ANY(focus_ids)
                      ELSE FALSE
                  END
              )
          ELSE TRUE
          END
      )
      AND q.exam_location IS NOT NULL
  ),
  location_counts AS (
    SELECT
      exam_location::text as location_id,
      COUNT(*) as count
    FROM filtered_questions
    GROUP BY exam_location
    HAVING COUNT(*) > 0  -- Only include locations with questions
  )
  SELECT json_object_agg(location_id, count) INTO result
  FROM location_counts;

  RETURN COALESCE(result, '{}'::json);
END;
$$;
