import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

// <PERSON><PERSON><PERSON> ou recuperar session ID único
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('medication_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('medication_session_id', sessionId);
  }
  return sessionId;
};



// Obter informações do navegador
const getBrowserInfo = () => {
  return {
    userAgent: navigator.userAgent,
    // IP será obtido pelo servidor
  };
};

export type AnalyticsEventType = 'medication_view';

interface TrackEventParams {
  eventType: AnalyticsEventType;
  medicationId?: string;
  categoryId?: string;
  metadata?: Record<string, any>;
}

export const useMedicationAnalytics = () => {
  const trackEvent = useCallback(async ({
    eventType,
    medicationId,
    categoryId,
    metadata = {}
  }: TrackEventParams) => {
    try {
      const sessionId = getSessionId();
      const browserInfo = getBrowserInfo();

      // Verificar se o usuário está autenticado (opcional)
      const { data: { user } } = await supabase.auth.getUser();

      // Chamar a função do Supabase para registrar o evento (funciona para todos)
      const { data, error } = await supabase.rpc('track_medication_event', {
        p_session_id: sessionId,
        p_action_type: eventType,
        p_medication_id: medicationId || null,
        p_category_id: categoryId || null,
        p_metadata: metadata,
        p_user_agent: browserInfo.userAgent,
        p_ip_address: null, // Será preenchido pelo servidor
        p_user_id: user?.id || null // Null para usuários anônimos
      });

      // Silenciar erros de analytics para não afetar UX
      if (error) {
        // Analytics error - não bloquear UX
      }
    } catch (error) {
      // Analytics error - não bloquear UX
    }
  }, []);

  // Função para rastrear visualização de medicamento (não-bloqueante)
  const trackMedicationView = useCallback((medicationId: string, medicationName?: string, categoryId?: string) => {
    // Executar analytics de forma assíncrona sem bloquear a navegação
    trackEvent({
      eventType: 'medication_view', // Este valor deve estar na constraint CHECK
      medicationId,
      categoryId,
      metadata: { medication_name: medicationName }
    }).catch(error => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  return {
    trackMedicationView
  };
};
