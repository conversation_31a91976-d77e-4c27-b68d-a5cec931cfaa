
import { useEffect } from "react";

interface CategoryColorDebugProps {
  category: any;
  config: any;
}

export const CategoryColorDebug = ({ category, config }: CategoryColorDebugProps) => {
  // Os formatos de cor que tentaremos
  const colorFormats = [
    config.color, // Cor sólida (sem gradiente)
    `bg-gradient-to-br ${config.gradient}`,
    config.gradient,
    category.color || "bg-gray-100"
  ];

  return (
    <div className="mt-4 p-4 border border-gray-300 rounded-md">
      <h3 className="font-medium mb-2">Debug de cores para: {category.name}</h3>
      <div className="grid grid-cols-1 gap-2">
        {colorFormats.map((colorFormat, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className={`w-16 h-16 rounded-md border border-gray-300 ${
                colorFormat.includes("bg-gradient") 
                  ? colorFormat 
                  : colorFormat.startsWith("bg-") 
                    ? colorFormat 
                    : `bg-${colorFormat}`
              }`}
            ></div>
            <code className="text-xs">{colorFormat}</code>
          </div>
        ))}
      </div>
    </div>
  );
};
