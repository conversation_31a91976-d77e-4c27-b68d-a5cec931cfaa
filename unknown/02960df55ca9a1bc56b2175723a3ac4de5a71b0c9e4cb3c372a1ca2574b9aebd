
import { motion } from "framer-motion";
import { Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { VaccineDose } from "./types";

interface VaccineDoseCardProps {
  dose: VaccineDose;
}

export function VaccineDoseCard({ dose }: VaccineDoseCardProps) {
  const { toast } = useToast();

  const showVaccineInfo = (name: string, description: string | null) => {
    toast({
      title: name,
      description: description || "Sem descrição disponível",
      className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-w-md w-full",
      duration: 5000,
    });
  };

  const formatDoseNumber = (number: number, type: string) => {
    return `${number}º ${type === 'reforço' ? 'reforço' : 'dose'}`;
  };

  return (
    <motion.div
      key={dose.id}
      className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4 hover:shadow-md transition-all duration-300 hover:-translate-y-1 border border-gray-100 dark:border-gray-700"
      whileHover={{ scale: 1.02 }}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center shadow-inner">
            <span className="text-white text-xl">💉</span>
          </div>
          <div>
            <h3 className="font-semibold dark:text-white">{dose.vaccine.name}</h3>
            <div className="flex gap-2 mt-1">
              <Badge 
                variant="secondary" 
                className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 dark:text-gray-200"
              >
                {formatDoseNumber(dose.dose_number, dose.dose_type)}
              </Badge>
              <Badge 
                variant="outline"
                className="dark:text-gray-200 dark:border-gray-600"
              >
                {dose.type}
              </Badge>
            </div>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={() => showVaccineInfo(dose.vaccine.name, dose.vaccine.description)}
          className="hover:bg-blue-50 transition-colors dark:hover:bg-slate-700 dark:text-gray-300"
        >
          <Info className="h-5 w-5" />
        </Button>
      </div>

      {dose.related_vaccines && dose.related_vaccines.length > 0 && (
        <div className="mt-3 pl-14">
          <div className="text-sm text-gray-500 dark:text-gray-400">Inclui:</div>
          <div className="flex flex-wrap gap-2 mt-1">
            {dose.related_vaccines.map((relatedVaccine) => (
              <Badge
                key={relatedVaccine.id}
                variant="secondary"
                className="bg-blue-50 text-blue-700 cursor-pointer hover:bg-blue-100 transition-colors dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50"
                onClick={() => showVaccineInfo(relatedVaccine.name, relatedVaccine.description)}
              >
                {relatedVaccine.name}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );
}
