import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useEffect } from 'react';

export const useAnsweredQuestions = () => {
  const queryClient = useQueryClient();

  // Configurar um listener para atualizações na tabela user_answers
  useEffect(() => {
    const channel = supabase
      .channel('user_answers_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_answers'
      }, () => {
        // Quando houver qualquer mudança na tabela, invalidar a query
        console.log('🔄 [useAnsweredQuestions] Detectada mudança na tabela user_answers, atualizando dados...');
        queryClient.invalidateQueries({ queryKey: ['correct-questions'] });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  return useQuery({
    queryKey: ['correct-questions'],
    queryFn: async () => {
      // Obter o usuário atual
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      // Buscar apenas as questões que o usuário acertou
      const { data: userAnswers, error } = await supabase
        .from('user_answers')
        .select('question_id')
        .eq('user_id', userId)
        .eq('is_correct', true); // Apenas questões acertadas

      if (error) {
        console.error('❌ [useAnsweredQuestions] Erro ao buscar respostas:', error);
        throw error;
      }

      // Extrair IDs únicos das questões acertadas
      const correctQuestionIds = [...new Set(userAnswers?.map(answer => answer.question_id) || [])];

      // Se não houver questões acertadas, retornar null para não aplicar o filtro
      if (correctQuestionIds.length === 0) {
        return null;
      }

      return {
        ids: correctQuestionIds,
        count: correctQuestionIds.length
      };
    },
    staleTime: 30 * 1000 // 30 segundos (reduzido para atualizar mais frequentemente)
  });
};
