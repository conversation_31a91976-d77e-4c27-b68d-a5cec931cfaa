
import { motion } from "framer-motion";
import { VaccineDose } from "./types";
import { VaccineDoseCard } from "./VaccineDoseCard";

interface VaccineAgeGroupProps {
  age: string;
  doses: VaccineDose[];
  index: number;
}

export function VaccineAgeGroup({ age, doses, index }: VaccineAgeGroupProps) {
  return (
    <motion.div 
      className="mb-8 relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
    >
      <div className="absolute left-8 -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full shadow-lg dark:from-emerald-600 dark:to-emerald-800" />
      
      <div className="ml-16 mb-4">
        <h2 className="inline-block bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-2 rounded-r-full font-semibold shadow-md dark:from-emerald-700 dark:to-emerald-900">
          {age}
        </h2>
      </div>

      <div className="ml-16 space-y-4">
        {doses.map((dose) => (
          <VaccineDoseCard key={dose.id} dose={dose} />
        ))}
      </div>
    </motion.div>
  );
}
