import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useSiteAnalytics } from './useMedicationAnalytics';

/**
 * Hook para tracking automático de page views em todo o site
 * Rastreia automaticamente cada mudança de rota
 */
export const usePageAnalytics = () => {
  const location = useLocation();
  const { trackPageView } = useSiteAnalytics();
  const lastTrackedPath = useRef<string>('');

  useEffect(() => {
    const currentPath = location.pathname + location.search;
    
    // Evitar tracking duplicado da mesma página
    if (lastTrackedPath.current === currentPath) {
      return;
    }

    // Aguardar um pouco para garantir que a página carregou
    const timeoutId = setTimeout(() => {
      // Coletar informações adicionais da página
      const pageMetadata = {
        page_title: document.title,
        page_search: location.search,
        page_hash: location.hash,
        viewport_width: window.innerWidth,
        viewport_height: window.innerHeight,
        screen_width: window.screen.width,
        screen_height: window.screen.height,
        device_pixel_ratio: window.devicePixelRatio,
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        is_mobile: window.innerWidth <= 768,
        is_tablet: window.innerWidth > 768 && window.innerWidth <= 1024,
        is_desktop: window.innerWidth > 1024
      };

      // Fazer o tracking
      trackPageView(currentPath, pageMetadata);
      
      // Atualizar referência
      lastTrackedPath.current = currentPath;
    }, 100); // 100ms de delay para garantir que a página carregou

    return () => clearTimeout(timeoutId);
  }, [location.pathname, location.search, location.hash, trackPageView]);

  // Tracking de tempo na página (quando o usuário sai)
  useEffect(() => {
    const startTime = Date.now();

    return () => {
      const timeSpent = Date.now() - startTime;
      
      // Só trackear se ficou mais de 3 segundos na página
      if (timeSpent > 3000) {
        // Fazer tracking do tempo gasto (não-bloqueante)
        setTimeout(() => {
          trackPageView(location.pathname, {
            event_type: 'page_exit',
            time_spent_ms: timeSpent,
            time_spent_seconds: Math.round(timeSpent / 1000)
          });
        }, 0);
      }
    };
  }, [location.pathname, trackPageView]);
};

/**
 * Hook para tracking de eventos específicos de páginas
 */
export const usePageEventTracking = () => {
  const { trackEvent } = useSiteAnalytics();

  // Tracking de scroll depth
  const trackScrollDepth = (depth: number) => {
    trackEvent({
      eventType: 'page_view',
      metadata: {
        event_type: 'scroll_depth',
        scroll_depth_percent: depth,
        page_url: window.location.pathname
      }
    });
  };

  // Tracking de cliques em links externos
  const trackExternalLink = (url: string, linkText?: string) => {
    trackEvent({
      eventType: 'page_view',
      metadata: {
        event_type: 'external_link_click',
        external_url: url,
        link_text: linkText,
        page_url: window.location.pathname
      }
    });
  };

  // Tracking de downloads
  const trackDownload = (fileName: string, fileType?: string) => {
    trackEvent({
      eventType: 'download',
      metadata: {
        file_name: fileName,
        file_type: fileType,
        page_url: window.location.pathname
      }
    });
  };

  return {
    trackScrollDepth,
    trackExternalLink,
    trackDownload
  };
};
