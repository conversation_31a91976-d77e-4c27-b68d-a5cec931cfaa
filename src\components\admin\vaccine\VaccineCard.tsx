import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Pencil, Trash2, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface VaccineCardProps {
  vaccine: any;
  vaccineRelationships: Record<string, any[]>;
  onAddDose: (id: string) => void;
  onEdit: (vaccine: any) => void;
  onDelete: (vaccine: any) => void;
  formatDoseNumber: (number: number, type: string) => string;
  handleDeleteDose: (doseId: string, doseName: string) => void;
}

export function VaccineCard({
  vaccine,
  vaccineRelationships,
  onAddDose,
  onEdit,
  onDelete,
  formatDoseNumber,
  handleDeleteDose,
}: VaccineCardProps) {
  const { toast } = useToast();

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-xl">{vaccine.name}</CardTitle>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onAddDose(vaccine.id)}
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(vaccine)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDelete(vaccine)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              toast({
                title: vaccine.name,
                description: vaccine.description,
                className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 whitespace-pre-line",
              });
            }}
          >
            <Info className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground whitespace-pre-line">
          {vaccine.description}
        </p>
        
        {vaccineRelationships?.[vaccine.id]?.length > 0 && (
          <div className="mt-4">
            <h4 className="font-semibold mb-2">Vacinas Relacionadas:</h4>
            <ul className="space-y-1">
              {vaccineRelationships[vaccine.id].map((relatedVaccine: any) => (
                <li key={relatedVaccine.id} className="text-sm text-muted-foreground">
                  • {relatedVaccine.name}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="mt-4">
          <h4 className="font-semibold mb-2">Doses:</h4>
          <ul className="space-y-2">
            {vaccine.pedbook_vaccine_doses?.sort((a: any, b: any) => {
              const ageA = parseInt(a.age_recommendation);
              const ageB = parseInt(b.age_recommendation);
              return ageA - ageB;
            }).map((dose: any) => {
              return (
                <li key={dose.id} className="text-sm flex items-center justify-between">
                  <span>
                    {formatDoseNumber(dose.dose_number, dose.dose_type)} - {
                      dose.age_recommendation === "0" 
                        ? "Ao nascer"
                        : parseInt(dose.age_recommendation) >= 12
                          ? `${Math.floor(parseInt(dose.age_recommendation) / 12)} ano(s) e ${parseInt(dose.age_recommendation) % 12} mese(s)`
                          : `${dose.age_recommendation} meses`
                    }
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteDose(dose.id, formatDoseNumber(dose.dose_number, dose.dose_type))}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </li>
              );
            })}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}