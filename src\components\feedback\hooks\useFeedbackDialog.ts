
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

// Função para gerar UUID compatível com todos os navegadores
const generateUUID = (): string => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback para navegadores que não suportam crypto.randomUUID
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

export const useFeedbackDialog = () => {
  const [open, setOpen] = useState(false);
  const [rating, setRating] = useState<number>(5); // Iniciando com 5 estrelas
  const [comment, setComment] = useState("");
  const [whatsapp, setWhatsapp] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const checkAndShowDialog = () => {
      const hasSubmitted = localStorage.getItem('feedback_submitted');
      if (hasSubmitted) return;

      const postponedTime = localStorage.getItem('feedback_postponed');
      if (postponedTime) {
        const postponedAt = parseInt(postponedTime);
        const now = Date.now();
        const hoursSincePostponed = (now - postponedAt) / (1000 * 60 * 60);

        if (hoursSincePostponed < 24) {
          return;
        }
      }

      const visitorId = localStorage.getItem('visitor_feedback_id');
      if (!visitorId) {
        const newVisitorId = generateUUID();
        localStorage.setItem('visitor_feedback_id', newVisitorId);
      }

      console.log('Iniciando timer para feedback dialog...');
      const timer = setTimeout(() => {
        console.log('Timer completado, abrindo dialog...');
        setOpen(true);
      }, 120000); // 2 minutos

      return () => {
        console.log('Limpando timer...');
        clearTimeout(timer);
      };
    };

    checkAndShowDialog();
  }, []);

  const handleSubmit = async () => {
    if (rating === 0) {
      toast({
        title: "Avaliação necessária",
        description: "Por favor, selecione uma classificação antes de enviar.",
        variant: "destructive",
      });
      return;
    }

    if (rating <= 4 && !whatsapp.trim()) {
      toast({
        title: "WhatsApp necessário",
        description: "Por favor, forneça seu WhatsApp para que possamos entender melhor como melhorar.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    const visitorId = localStorage.getItem('visitor_feedback_id');

    try {
      const { error } = await supabase
        .from('site_visitor_feedbacks')
        .insert([
          {
            rating,
            comment,
            whatsapp: whatsapp || null,
            visitor_id: visitorId
          }
        ]);

      if (error) throw error;

      localStorage.setItem('feedback_submitted', 'true');
      toast({
        title: "Feedback enviado!",
        description: "Obrigado por compartilhar sua opinião conosco.",
      });

      // Garantimos que o dialog fecha corretamente
      setOpen(false);

      // Reset form
      setRating(5);
      setComment("");
      setWhatsapp("");
    } catch (error) {
      toast({
        title: "Erro ao enviar feedback",
        description: "Por favor, tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    localStorage.setItem('feedback_postponed', Date.now().toString());
    console.log('Feedback postponed, closing dialog...');
    setOpen(false);

    // Reset form on close to avoid stale data
    setRating(5);
    setComment("");
    setWhatsapp("");
  };

  return {
    open,
    setOpen,
    rating,
    setRating,
    comment,
    setComment,
    whatsapp,
    setWhatsapp,
    isSubmitting,
    handleSubmit,
    handleClose
  };
};
