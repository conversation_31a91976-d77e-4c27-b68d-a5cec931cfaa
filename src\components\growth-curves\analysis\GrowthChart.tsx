import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>xis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { ChartLegend } from './chart/ChartLegend';
import { COLORS, MEASUREMENT_LABELS } from './constants';
import { useEffect, useRef, useState } from 'react';

interface ChartData {
  age: number;
  p3: number;
  p15: number;
  p50: number;
  p85: number;
  p97: number;
  patient?: number;
}

interface GrowthChartProps {
  data: ChartData[];
  measurementType: 'weight' | 'height' | 'bmi' | 'head-circumference';
}

const formatXAxisTick = (value: number) => {
  const years = Math.floor(value / 12);
  const months = value % 12;
  
  if (value === 0) return '0';
  if (months === 0) return `${years}a`;
  if (years === 0) return `${months}m`;
  return `${months}m`;
};

const formatTooltipValue = (value: number, measurementType: string) => {
  const unit = measurementType === 'weight' ? 'kg' : 
              measurementType === 'height' ? 'cm' : 
              measurementType === 'bmi' ? 'kg/m²' :
              'cm';
  return `${value.toFixed(1)} ${unit}`;
};

const formatTooltipLabel = (age: number) => {
  const years = Math.floor(age / 12);
  const months = age % 12;
  
  if (years === 0) return `${age} meses`;
  if (months === 0) return `${years} ${years === 1 ? 'ano' : 'anos'}`;
  return `${years} ${years === 1 ? 'ano' : 'anos'} e ${months} ${months === 1 ? 'mês' : 'meses'}`;
};

export function GrowthChart({ data, measurementType }: GrowthChartProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          setDimensions({ width, height });
        }
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  return (
    <div ref={containerRef} className="w-full h-[500px] min-h-[500px]">
      {dimensions.width > 0 && dimensions.height > 0 && (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart 
            data={data}
            margin={{ top: 5, right: 0, left: -20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="age" 
              tick={{ fontSize: 10 }}
              tickFormatter={formatXAxisTick}
              interval={2}
              domain={[0, 'auto']}
            />
            <YAxis 
              tick={{ fontSize: 10 }}
              domain={['auto', 'auto']}
            />
            <Tooltip 
              formatter={(value: number, name: string) => [
                formatTooltipValue(value, measurementType),
                name === 'patient' ? 'Paciente' : name
              ]}
              labelFormatter={formatTooltipLabel}
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                padding: '8px',
                fontSize: '12px'
              }}
            />
            <Legend 
              content={props => <ChartLegend {...props} measurementType={measurementType} />}
            />
            {Object.entries(COLORS).map(([key, color]) => (
              key !== 'patient' && (
                <Line 
                  key={key}
                  type="monotone" 
                  dataKey={key} 
                  stroke={color}
                  dot={false}
                  strokeDasharray={['p3', 'p15', 'p85', 'p97'].includes(key) ? '5 5' : undefined}
                  strokeWidth={key === 'p50' ? 2 : 1}
                />
              )
            ))}
            <Line
              type="monotone"
              dataKey="patient"
              stroke={COLORS.patient}
              strokeWidth={2}
              dot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      )}
      <div className="text-center text-[8px] text-gray-500 mt-2">
        WHO Child Growth Standards
      </div>
    </div>
  );
}